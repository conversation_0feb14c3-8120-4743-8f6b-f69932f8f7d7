<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductReturn;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ReturnController extends Controller
{
    /**
     * Display returns
     */
    public function index(): Response
    {
        $returns = ProductReturn::with(['user:id,name', 'product'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Return/Index', [
            'returns' => $returns,
        ]);
    }

    /**
     * Show return form
     */
    public function create(): Response
    {
        $products = Product::orderBy('name')->get();

        return Inertia::render('Return/Create', [
            'products' => $products,
        ]);
    }

    /**
     * Store a new return
     */
    public function store(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'price' => 'required|numeric|min:0',
            'reason' => 'required|string|max:500',
        ]);

        ProductReturn::create([
            'user_id' => auth()->id(),
            'product_id' => $request->product_id,
            'quantity' => $request->quantity,
            'price' => $request->price,
            'reason' => $request->reason,
        ]);

        return redirect()->route('return.index')
            ->with('success', 'Return request submitted successfully.');
    }

    /**
     * Show return details
     */
    public function show($id): Response
    {
        $return = ProductReturn::with(['user:id,name', 'product'])
            ->findOrFail($id);

        return Inertia::render('Return/Show', [
            'return' => $return,
        ]);
    }

    /**
     * Approve a return
     */
    public function approve($id)
    {
        $return = ProductReturn::findOrFail($id);
        $return->approve();

        return redirect()->back()
            ->with('success', 'Return approved successfully.');
    }

    /**
     * Reject a return
     */
    public function reject($id)
    {
        $return = ProductReturn::findOrFail($id);
        $return->reject();

        return redirect()->back()
            ->with('success', 'Return rejected.');
    }
}
