<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { ArrowLeft, Package, FileText, Receipt, CheckCircle, X } from 'lucide-vue-next';
import { ref } from 'vue';

interface Product {
    id: number;
    name: string;
    quantity: number;
}

interface PoRecievedItem {
    id: number;
    product_name: string;
    quantity: number;
    price: number;
    total: number;
}

interface PoRecieved {
    id: number;
    po_number: string;
    po_date: string;
    institution_name: string;
    status: string;
    total_amount: number;
    user: {
        id: number;
        name: string;
    };
    items: PoRecievedItem[];
}

interface Props {
    po: PoRecieved;
    products: Product[];
    suppliedQuantities: Record<number, number>;
}

const props = defineProps<Props>();

const form = useForm({
    supplies: props.po.items.map(item => ({
        po_item_id: item.id,
        quantity: 0
    })),
    notes: ''
});

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const getRemainingQuantity = (item: PoRecievedItem) => {
    const supplied = props.suppliedQuantities[item.id] || 0;
    return item.quantity - supplied;
};

const showDocumentPopup = ref(false);
const isGeneratingDocuments = ref(false);

const submit = () => {
    // Filter out items with 0 quantity
    const filteredSupplies = form.supplies.filter(supply => supply.quantity > 0);

    if (filteredSupplies.length === 0) {
        alert('Please enter quantities to supply.');
        return;
    }

    form.supplies = filteredSupplies;

    form.post(route('supply.store', props.po.id), {
        onSuccess: (page) => {
            // Show the document generation popup
            showDocumentPopup.value = true;
        }
    });
};

const generateDcAndInvoice = async () => {
    isGeneratingDocuments.value = true;

    try {
        const response = await fetch(`/documents/generate/${props.po.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
        });

        const data = await response.json();

        if (data.success) {
            // Close popup
            showDocumentPopup.value = false;

            // Open documents in new tabs
            window.open(`/documents/dc/${data.dc_id}/print`, '_blank');
            window.open(`/documents/invoice/${data.invoice_id}/print`, '_blank');

            // Redirect to documents page
            setTimeout(() => {
                window.location.href = '/documents';
            }, 1000);
        } else {
            alert('Error generating documents: ' + data.message);
        }
    } catch (error) {
        console.error('Error generating documents:', error);
        alert('Error generating documents. Please try again.');
    } finally {
        isGeneratingDocuments.value = false;
    }
};

const closePopup = () => {
    showDocumentPopup.value = false;
    // Redirect to supply index
    window.location.href = '/supply';
};
</script>

<template>
    <Head :title="`Supply PO - ${po.institution_name}`" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center space-x-4">
                <Button variant="outline" size="sm" as-child>
                    <Link :href="route('supply.index')">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back
                    </Link>
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Supply PO</h1>
                    <p class="text-muted-foreground">
                        Process supply for {{ po.institution_name }}
                    </p>
                </div>
            </div>

            <!-- PO Details -->
            <Card>
                <CardHeader>
                    <CardTitle>PO Details</CardTitle>
                    <CardDescription>
                        PO #{{ po.po_number }} • {{ formatDate(po.po_date) }}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <p class="font-medium">Institution</p>
                            <p class="text-muted-foreground">{{ po.institution_name }}</p>
                        </div>
                        <div>
                            <p class="font-medium">Status</p>
                            <p class="text-muted-foreground">{{ po.status.replace('_', ' ').toUpperCase() }}</p>
                        </div>
                        <div>
                            <p class="font-medium">Total Amount</p>
                            <p class="text-muted-foreground">{{ formatCurrency(po.total_amount) }}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Supply Form -->
            <form @submit.prevent="submit">
                <Card>
                    <CardHeader>
                        <CardTitle>Supply Items</CardTitle>
                        <CardDescription>
                            Enter quantities to supply for each item
                        </CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div v-for="(item, index) in po.items" :key="item.id" class="border rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <p class="font-medium">Product</p>
                                    <p class="text-muted-foreground">{{ item.product_name }}</p>
                                </div>
                                <div>
                                    <p class="font-medium">Requested</p>
                                    <p class="text-muted-foreground">{{ item.quantity }}</p>
                                </div>
                                <div>
                                    <p class="font-medium">Already Supplied</p>
                                    <p class="text-muted-foreground">{{ suppliedQuantities[item.id] || 0 }}</p>
                                </div>
                                <div>
                                    <p class="font-medium">Remaining</p>
                                    <p class="text-muted-foreground font-semibold">{{ getRemainingQuantity(item) }}</p>
                                </div>
                                <div>
                                    <Label :for="`quantity_${index}`">Supply Quantity</Label>
                                    <Input
                                        :id="`quantity_${index}`"
                                        v-model.number="form.supplies[index].quantity"
                                        type="number"
                                        :max="getRemainingQuantity(item)"
                                        min="0"
                                        placeholder="0"
                                    />
                                    <InputError :message="form.errors[`supplies.${index}.quantity`]" />
                                </div>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <Label for="notes">Notes (Optional)</Label>
                            <textarea
                                id="notes"
                                v-model="form.notes"
                                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                placeholder="Add any notes about this supply..."
                            ></textarea>
                            <InputError :message="form.errors.notes" />
                        </div>

                        <div class="flex items-center justify-end space-x-4 pt-4">
                            <Button type="button" variant="outline" as-child>
                                <Link :href="route('supply.index')">Cancel</Link>
                            </Button>
                            <Button type="submit" :disabled="form.processing">
                                {{ form.processing ? 'Processing...' : 'Process Supply' }}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </form>
        </div>

        <!-- Document Generation Popup -->
        <div v-if="showDocumentPopup" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4 shadow-2xl">
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                        <CheckCircle class="h-6 w-6 text-green-600" />
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Supply Processed Successfully!</h3>
                    <p class="text-sm text-gray-500 mb-6">
                        Do you want to generate DC (Delivery Challan) and Invoice for this supply?
                    </p>

                    <div class="flex items-center justify-center space-x-4 mb-6">
                        <div class="flex items-center space-x-2">
                            <FileText class="h-5 w-5 text-blue-600" />
                            <span class="text-sm font-medium">Delivery Challan</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <Receipt class="h-5 w-5 text-green-600" />
                            <span class="text-sm font-medium">Tax Invoice</span>
                        </div>
                    </div>

                    <div class="flex space-x-3">
                        <Button
                            @click="generateDcAndInvoice"
                            :disabled="isGeneratingDocuments"
                            class="flex-1"
                        >
                            <FileText class="mr-2 h-4 w-4" />
                            {{ isGeneratingDocuments ? 'Generating...' : 'Generate DC & Invoice' }}
                        </Button>
                        <Button
                            @click="closePopup"
                            variant="outline"
                            :disabled="isGeneratingDocuments"
                        >
                            <X class="mr-2 h-4 w-4" />
                            Skip
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
