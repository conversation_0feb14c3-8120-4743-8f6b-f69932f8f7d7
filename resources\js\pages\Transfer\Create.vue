<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { ArrowLeft } from 'lucide-vue-next';
import { computed } from 'vue';

interface Product {
    id: number;
    name: string;
    quantity: number;
}

interface User {
    id: number;
    name: string;
    email: string;
}

interface Props {
    products: Product[];
    users: User[];
}

const props = defineProps<Props>();

const form = useForm({
    to_user_id: '',
    product_id: '',
    quantity: 1,
    notes: ''
});

const submit = () => {
    form.post(route('transfer.store'));
};

const getMaxQuantity = () => {
    if (!form.product_id) return 0;
    const selectedProduct = props.products.find(p => p.id.toString() === form.product_id);
    return selectedProduct ? selectedProduct.quantity : 0;
};

const selectedProduct = computed(() => {
    if (!form.product_id) return null;
    return props.products.find(p => p.id.toString() === form.product_id);
});
</script>

<template>
    <Head title="Create Transfer" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center space-x-4">
                <Button variant="outline" size="sm" as-child>
                    <Link :href="route('transfer.index')">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back
                    </Link>
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Create Transfer</h1>
                    <p class="text-muted-foreground">
                        Transfer products to another user
                    </p>
                </div>
            </div>

            <Card class="max-w-2xl">
                <CardHeader>
                    <CardTitle>Transfer Details</CardTitle>
                    <CardDescription>
                        Select the product and user to transfer to
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="submit" class="space-y-4">
                        <div class="space-y-2">
                            <Label for="to_user_id">Transfer To</Label>
                            <select
                                id="to_user_id"
                                v-model="form.to_user_id"
                                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                required
                            >
                                <option value="">Select a user</option>
                                <option v-for="user in users" :key="user.id" :value="user.id">
                                    {{ user.name }} ({{ user.email }})
                                </option>
                            </select>
                            <InputError :message="form.errors.to_user_id" />
                        </div>

                        <div class="space-y-2">
                            <Label for="product_id">Product</Label>
                            <select
                                id="product_id"
                                v-model="form.product_id"
                                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                required
                            >
                                <option value="">Select a product</option>
                                <option v-for="product in products" :key="product.id" :value="product.id">
                                    {{ product.name }} ({{ product.quantity }} available)
                                </option>
                            </select>
                            <InputError :message="form.errors.product_id" />
                        </div>

                        <div class="space-y-2">
                            <Label for="quantity">Quantity</Label>
                            <Input
                                id="quantity"
                                v-model.number="form.quantity"
                                type="number"
                                :max="getMaxQuantity()"
                                min="1"
                                :disabled="!form.product_id"
                                required
                            />
                            <p v-if="selectedProduct" class="text-sm text-muted-foreground">
                                Maximum available: {{ selectedProduct.quantity }} units
                            </p>
                            <p v-else-if="form.product_id" class="text-sm text-red-500">
                                Product not found or no stock available
                            </p>
                            <InputError :message="form.errors.quantity" />
                        </div>

                        <div class="space-y-2">
                            <Label for="notes">Notes (Optional)</Label>
                            <textarea
                                id="notes"
                                v-model="form.notes"
                                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                placeholder="Add any notes about this transfer..."
                            ></textarea>
                            <InputError :message="form.errors.notes" />
                        </div>

                        <div class="flex items-center justify-end space-x-4 pt-4">
                            <Button type="button" variant="outline" as-child>
                                <Link :href="route('transfer.index')">Cancel</Link>
                            </Button>
                            <Button type="submit" :disabled="form.processing">
                                {{ form.processing ? 'Creating...' : 'Create Transfer' }}
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
