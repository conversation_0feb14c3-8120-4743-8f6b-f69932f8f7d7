<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\PoRecieved;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments
     */
    public function index(): Response
    {
        $payments = Payment::with(['poRecieved', 'user:id,name'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Payment/Index', [
            'payments' => $payments,
        ]);
    }

    /**
     * Show the form for creating a new payment
     */
    public function create(): Response
    {
        // Get supplied POs that don't have payments yet
        $suppliedPos = PoRecieved::where('status', 'supplied')
            ->whereDoesntHave('payments')
            ->with(['user:id,name'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('Payment/Create', [
            'suppliedPos' => $suppliedPos,
        ]);
    }

    /**
     * Store a newly created payment
     */
    public function store(Request $request)
    {
        $request->validate([
            'po_recieved_id' => 'required|exists:po_recieved,id',
            'cheque_number' => 'required|string|max:255',
            'cheque_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:500',
        ]);

        $chequeImagePath = null;
        if ($request->hasFile('cheque_image')) {
            $chequeImagePath = $request->file('cheque_image')->store('payments', 'public');
        }

        Payment::create([
            'po_recieved_id' => $request->po_recieved_id,
            'user_id' => auth()->id(),
            'cheque_number' => $request->cheque_number,
            'cheque_image' => $chequeImagePath,
            'amount' => $request->amount,
            'payment_date' => $request->payment_date,
            'notes' => $request->notes,
        ]);

        return redirect()->route('payment.index')
            ->with('success', 'Payment recorded successfully.');
    }

    /**
     * Display the specified payment
     */
    public function show($id): Response
    {
        $payment = Payment::with(['poRecieved.items', 'user:id,name'])
            ->findOrFail($id);

        return Inertia::render('Payment/Show', [
            'payment' => $payment,
        ]);
    }

    /**
     * Show the form for editing the specified payment
     */
    public function edit($id): Response
    {
        $payment = Payment::findOrFail($id);
        
        // Check if user can edit this payment
        if (auth()->user()->role !== 'admin' && $payment->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to this payment.');
        }

        return Inertia::render('Payment/Edit', [
            'payment' => $payment,
        ]);
    }

    /**
     * Update the specified payment
     */
    public function update(Request $request, $id)
    {
        $payment = Payment::findOrFail($id);
        
        // Check if user can edit this payment
        if (auth()->user()->role !== 'admin' && $payment->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to this payment.');
        }

        $request->validate([
            'cheque_number' => 'required|string|max:255',
            'cheque_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:500',
        ]);

        $chequeImagePath = $payment->cheque_image;
        if ($request->hasFile('cheque_image')) {
            // Delete old image
            if ($payment->cheque_image) {
                Storage::disk('public')->delete($payment->cheque_image);
            }
            $chequeImagePath = $request->file('cheque_image')->store('payments', 'public');
        }

        $payment->update([
            'cheque_number' => $request->cheque_number,
            'cheque_image' => $chequeImagePath,
            'amount' => $request->amount,
            'payment_date' => $request->payment_date,
            'notes' => $request->notes,
        ]);

        return redirect()->route('payment.index')
            ->with('success', 'Payment updated successfully.');
    }

    /**
     * Remove the specified payment
     */
    public function destroy($id)
    {
        $payment = Payment::findOrFail($id);
        
        // Check if user can delete this payment
        if (auth()->user()->role !== 'admin' && $payment->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to this payment.');
        }

        // Delete cheque image
        if ($payment->cheque_image) {
            Storage::disk('public')->delete($payment->cheque_image);
        }

        $payment->delete();

        return redirect()->route('payment.index')
            ->with('success', 'Payment deleted successfully.');
    }

    /**
     * Approve a payment (admin only)
     */
    public function approve($id)
    {
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Only admin can approve payments.');
        }

        $payment = Payment::findOrFail($id);
        $payment->update(['status' => 'approved']);

        return redirect()->back()
            ->with('success', 'Payment approved successfully.');
    }

    /**
     * Reject a payment (admin only)
     */
    public function reject($id)
    {
        if (auth()->user()->role !== 'admin') {
            abort(403, 'Only admin can reject payments.');
        }

        $payment = Payment::findOrFail($id);
        $payment->update(['status' => 'rejected']);

        return redirect()->back()
            ->with('success', 'Payment rejected.');
    }
}
