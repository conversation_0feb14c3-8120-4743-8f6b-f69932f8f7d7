<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { Package, TrendingUp, TrendingDown, Plus } from 'lucide-vue-next';

interface StockMovement {
    id: number;
    type: 'in' | 'out';
    quantity: number;
    reference_type: string;
    notes: string;
    created_at: string;
    user: {
        id: number;
        name: string;
    };
}

interface Product {
    id: number;
    name: string;
    quantity: number;
    created_at: string;
    stock_movements: StockMovement[];
}

interface Props {
    products: {
        data: Product[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const getTotalStockIn = (product: Product) => {
    return product.stock_movements
        .filter(movement => movement.type === 'in')
        .reduce((total, movement) => total + movement.quantity, 0);
};

const getTotalStockOut = (product: Product) => {
    return product.stock_movements
        .filter(movement => movement.type === 'out')
        .reduce((total, movement) => total + movement.quantity, 0);
};

const getStockStatus = (quantity: number) => {
    if (quantity === 0) return { color: 'text-red-600 bg-red-100', status: 'Out of Stock' };
    if (quantity < 10) return { color: 'text-yellow-600 bg-yellow-100', status: 'Low Stock' };
    return { color: 'text-green-600 bg-green-100', status: 'In Stock' };
};
</script>

<template>
    <Head title="Stock Overview" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Stock Overview</h1>
                    <p class="text-muted-foreground">
                        View remaining stock levels and movement history
                    </p>
                </div>
                <Button as-child>
                    <Link :href="route('product.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        Add Product
                    </Link>
                </Button>
            </div>

            <div class="grid gap-4">
                <Card v-for="product in products.data" :key="product.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    <Package class="h-5 w-5" />
                                    {{ product.name }}
                                    <span :class="`px-2 py-1 text-xs font-medium rounded-full ${getStockStatus(product.quantity).color}`">
                                        {{ getStockStatus(product.quantity).status }}
                                    </span>
                                </CardTitle>
                                <CardDescription>
                                    Created on {{ formatDate(product.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="text-right">
                                <p class="text-3xl font-bold">{{ product.quantity }}</p>
                                <p class="text-sm text-muted-foreground">units remaining</p>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
                                    <TrendingUp class="h-5 w-5 text-green-600" />
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Total Stock In</p>
                                    <p class="text-lg font-semibold text-green-600">{{ getTotalStockIn(product) }}</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center justify-center w-10 h-10 bg-red-100 rounded-full">
                                    <TrendingDown class="h-5 w-5 text-red-600" />
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Total Stock Out</p>
                                    <p class="text-lg font-semibold text-red-600">{{ getTotalStockOut(product) }}</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                                    <Package class="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Current Stock</p>
                                    <p class="text-lg font-semibold text-blue-600">{{ product.quantity }}</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <Button size="sm" variant="outline" as-child>
                                    <Link :href="route('product.show', product.id)">
                                        View Details
                                    </Link>
                                </Button>
                                <Button size="sm" variant="outline" as-child>
                                    <Link :href="route('product.edit', product.id)">
                                        Edit
                                    </Link>
                                </Button>
                            </div>
                        </div>
                        
                        <!-- Recent Movements -->
                        <div v-if="product.stock_movements.length > 0" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Recent Movements</h4>
                            <div class="space-y-2">
                                <div 
                                    v-for="movement in product.stock_movements.slice(0, 3)" 
                                    :key="movement.id"
                                    class="flex items-center justify-between text-sm p-2 bg-gray-50 rounded"
                                >
                                    <div class="flex items-center space-x-2">
                                        <TrendingUp v-if="movement.type === 'in'" class="h-4 w-4 text-green-600" />
                                        <TrendingDown v-else class="h-4 w-4 text-red-600" />
                                        <span>{{ movement.reference_type }}</span>
                                        <span class="text-gray-500">by {{ movement.user.name }}</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span :class="movement.type === 'in' ? 'text-green-600' : 'text-red-600'">
                                            {{ movement.type === 'in' ? '+' : '-' }}{{ movement.quantity }}
                                        </span>
                                        <span class="text-gray-500">{{ formatDate(movement.created_at) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <div v-if="products.data.length === 0" class="text-center py-12">
                    <Package class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No products</h3>
                    <p class="mt-1 text-sm text-muted-foreground">Get started by creating your first product.</p>
                    <div class="mt-6">
                        <Button as-child>
                            <Link :href="route('product.create')">
                                <Plus class="mr-2 h-4 w-4" />
                                Add Product
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
