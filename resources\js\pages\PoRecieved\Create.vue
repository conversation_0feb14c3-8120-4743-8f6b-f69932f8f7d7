<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { Plus, Trash2 } from 'lucide-vue-next';

interface PoItem {
    product_name: string;
    quantity: number;
    price: number;
}

const form = useForm({
    po_number: '',
    po_date: '',
    po_image: null as File | null,
    institution_name: '',
    address: '',
    email: '',
    phone: '',
    items: [
        {
            product_name: '',
            quantity: 1,
            price: 0
        }
    ] as PoItem[]
});

const addItem = () => {
    form.items.push({
        product_name: '',
        quantity: 1,
        price: 0
    });
};

const removeItem = (index: number) => {
    if (form.items.length > 1) {
        form.items.splice(index, 1);
    }
};

const calculateItemTotal = (item: PoItem) => {
    return item.quantity * item.price;
};

const calculateGrandTotal = () => {
    return form.items.reduce((total, item) => total + calculateItemTotal(item), 0);
};

const handleFileChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        form.po_image = target.files[0];
    }
};

const submit = () => {
    form.post(route('po-recieved.store'));
};
</script>

<template>
    <Head title="Create PO Recieved" />

    <AppLayout>
        <div class="space-y-6">
            <div>
                <h1 class="text-3xl font-bold tracking-tight">Create PO Recieved</h1>
                <p class="text-muted-foreground">
                    Add a new purchase order recieved from an institution
                </p>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
                <!-- PO Details -->
                <Card>
                    <CardHeader>
                        <CardTitle>PO Details</CardTitle>
                        <CardDescription>
                            Enter the purchase order information
                        </CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="po_number">PO Number</Label>
                                <Input
                                    id="po_number"
                                    v-model="form.po_number"
                                    type="text"
                                    required
                                />
                                <InputError :message="form.errors.po_number" />
                            </div>
                            <div class="space-y-2">
                                <Label for="po_date">PO Date</Label>
                                <Input
                                    id="po_date"
                                    v-model="form.po_date"
                                    type="date"
                                    required
                                />
                                <InputError :message="form.errors.po_date" />
                            </div>
                        </div>
                        <div class="space-y-2">
                            <Label for="po_image">PO Image</Label>
                            <Input
                                id="po_image"
                                type="file"
                                accept="image/*"
                                @change="handleFileChange"
                            />
                            <InputError :message="form.errors.po_image" />
                        </div>
                    </CardContent>
                </Card>

                <!-- Institution Details -->
                <Card>
                    <CardHeader>
                        <CardTitle>Institution Details</CardTitle>
                        <CardDescription>
                            Enter the details of the institution/hospital
                        </CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="institution_name">Institution/Hospital Name</Label>
                                <Input
                                    id="institution_name"
                                    v-model="form.institution_name"
                                    type="text"
                                    required
                                />
                                <InputError :message="form.errors.institution_name" />
                            </div>
                            <div class="space-y-2">
                                <Label for="email">Email</Label>
                                <Input
                                    id="email"
                                    v-model="form.email"
                                    type="email"
                                    required
                                />
                                <InputError :message="form.errors.email" />
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="phone">Phone</Label>
                                <Input
                                    id="phone"
                                    v-model="form.phone"
                                    type="tel"
                                    required
                                />
                                <InputError :message="form.errors.phone" />
                            </div>
                        </div>
                        <div class="space-y-2">
                            <Label for="address">Address</Label>
                            <textarea
                                id="address"
                                v-model="form.address"
                                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                required
                            ></textarea>
                            <InputError :message="form.errors.address" />
                        </div>
                    </CardContent>
                </Card>

                <!-- Products/Items -->
                <Card>
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle>Products/Items</CardTitle>
                                <CardDescription>
                                    Add products and their details
                                </CardDescription>
                            </div>
                            <Button type="button" @click="addItem" variant="outline">
                                <Plus class="mr-2 h-4 w-4" />
                                Add Item
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div v-for="(item, index) in form.items" :key="index" class="border rounded-lg p-4 space-y-4">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium">Item {{ index + 1 }}</h4>
                                <Button
                                    v-if="form.items.length > 1"
                                    type="button"
                                    @click="removeItem(index)"
                                    variant="destructive"
                                    size="sm"
                                >
                                    <Trash2 class="h-4 w-4" />
                                </Button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div class="space-y-2">
                                    <Label :for="`product_name_${index}`">Product Name</Label>
                                    <Input
                                        :id="`product_name_${index}`"
                                        v-model="item.product_name"
                                        type="text"
                                        required
                                    />
                                    <InputError :message="form.errors[`items.${index}.product_name`]" />
                                </div>
                                <div class="space-y-2">
                                    <Label :for="`quantity_${index}`">Quantity</Label>
                                    <Input
                                        :id="`quantity_${index}`"
                                        v-model.number="item.quantity"
                                        type="number"
                                        min="1"
                                        required
                                    />
                                    <InputError :message="form.errors[`items.${index}.quantity`]" />
                                </div>
                                <div class="space-y-2">
                                    <Label :for="`price_${index}`">Price</Label>
                                    <Input
                                        :id="`price_${index}`"
                                        v-model.number="item.price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        required
                                    />
                                    <InputError :message="form.errors[`items.${index}.price`]" />
                                </div>
                                <div class="space-y-2">
                                    <Label>Total</Label>
                                    <div class="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm">
                                        ${{ calculateItemTotal(item).toFixed(2) }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="border-t pt-4">
                            <div class="text-right">
                                <p class="text-lg font-semibold">
                                    Grand Total: ${{ calculateGrandTotal().toFixed(2) }}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <div class="flex items-center justify-end space-x-4">
                    <Button type="button" variant="outline" as-child>
                        <Link :href="route('po-recieved.index')">Cancel</Link>
                    </Button>
                    <Button type="submit" :disabled="form.processing">
                        {{ form.processing ? 'Creating...' : 'Create PO Recieved' }}
                    </Button>
                </div>
            </form>
        </div>
    </AppLayout>
</template>
