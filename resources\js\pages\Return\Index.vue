<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { Plus, RotateCcw, Check, X } from 'lucide-vue-next';

interface ProductReturn {
    id: number;
    quantity: number;
    price: number;
    reason: string;
    status: 'pending' | 'approved' | 'rejected';
    created_at: string;
    user: {
        id: number;
        name: string;
    };
    product: {
        id: number;
        name: string;
    };
}

interface Props {
    returns: {
        data: ProductReturn[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'text-yellow-600 bg-yellow-100';
        case 'approved':
            return 'text-green-600 bg-green-100';
        case 'rejected':
            return 'text-red-600 bg-red-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
};

const approveReturn = (id: number) => {
    if (confirm('Are you sure you want to approve this return?')) {
        router.post(route('return.approve', id));
    }
};

const rejectReturn = (id: number) => {
    if (confirm('Are you sure you want to reject this return?')) {
        router.post(route('return.reject', id));
    }
};
</script>

<template>
    <Head title="Returns" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Returns</h1>
                    <p class="text-muted-foreground">
                        Manage product return requests
                    </p>
                </div>
                <Button as-child>
                    <Link :href="route('return.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        New Return
                    </Link>
                </Button>
            </div>

            <div class="grid gap-4">
                <Card v-for="returnItem in returns.data" :key="returnItem.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    <RotateCcw class="h-5 w-5" />
                                    {{ returnItem.product.name }}
                                    <span :class="`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(returnItem.status)}`">
                                        {{ returnItem.status.toUpperCase() }}
                                    </span>
                                </CardTitle>
                                <CardDescription>
                                    Requested by {{ returnItem.user.name }} on {{ formatDate(returnItem.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="text-right mr-4">
                                    <p class="text-lg font-bold">{{ returnItem.quantity }} units</p>
                                    <p class="text-sm text-muted-foreground">{{ formatCurrency(returnItem.price) }} each</p>
                                </div>
                                <div v-if="returnItem.status === 'pending'" class="flex space-x-2">
                                    <Button size="sm" @click="approveReturn(returnItem.id)">
                                        <Check class="h-4 w-4" />
                                    </Button>
                                    <Button size="sm" variant="destructive" @click="rejectReturn(returnItem.id)">
                                        <X class="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div>
                            <p class="font-medium text-sm">Reason:</p>
                            <p class="text-sm text-muted-foreground">{{ returnItem.reason }}</p>
                        </div>
                        <div class="mt-2 text-sm">
                            <span class="font-medium">Total Value:</span>
                            {{ formatCurrency(returnItem.quantity * returnItem.price) }}
                        </div>
                    </CardContent>
                </Card>

                <div v-if="returns.data.length === 0" class="text-center py-12">
                    <RotateCcw class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No returns</h3>
                    <p class="mt-1 text-sm text-muted-foreground">Get started by creating a new return request.</p>
                    <div class="mt-6">
                        <Button as-child>
                            <Link :href="route('return.create')">
                                <Plus class="mr-2 h-4 w-4" />
                                New Return
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
