<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { Truck, Package } from 'lucide-vue-next';

interface PoRecievedItem {
    id: number;
    product_name: string;
    quantity: number;
    price: number;
    total: number;
}

interface PoRecieved {
    id: number;
    po_number: string;
    po_date: string;
    institution_name: string;
    status: 'pending' | 'partially_supplied' | 'supplied';
    total_amount: number;
    created_at: string;
    user: {
        id: number;
        name: string;
    };
    items: PoRecievedItem[];
}

interface Props {
    pendingPOs: {
        data: PoRecieved[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'text-yellow-600 bg-yellow-100';
        case 'partially_supplied':
            return 'text-blue-600 bg-blue-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
};
</script>

<template>
    <Head title="Supply Management" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Supply Management</h1>
                    <p class="text-muted-foreground">
                        Manage supplies for pending purchase orders
                    </p>
                </div>
            </div>

            <div class="grid gap-4">
                <Card v-for="po in pendingPOs.data" :key="po.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    {{ po.institution_name }}
                                    <span :class="`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(po.status)}`">
                                        {{ po.status.replace('_', ' ').toUpperCase() }}
                                    </span>
                                </CardTitle>
                                <CardDescription>
                                    PO #{{ po.po_number }} • Created by {{ po.user.name }} on {{ formatDate(po.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button as-child>
                                    <Link :href="route('supply.show', po.id)">
                                        <Truck class="mr-2 h-4 w-4" />
                                        Supply
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <p class="font-medium">PO Date</p>
                                <p class="text-muted-foreground">{{ formatDate(po.po_date) }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Items</p>
                                <p class="text-muted-foreground">{{ po.items.length }} items</p>
                            </div>
                            <div>
                                <p class="font-medium">Total Amount</p>
                                <p class="text-muted-foreground font-semibold">{{ formatCurrency(po.total_amount) }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Status</p>
                                <p class="text-muted-foreground">{{ po.status.replace('_', ' ') }}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <div v-if="pendingPOs.data.length === 0" class="text-center py-12">
                    <Package class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No pending POs</h3>
                    <p class="mt-1 text-sm text-muted-foreground">All purchase orders have been supplied.</p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
