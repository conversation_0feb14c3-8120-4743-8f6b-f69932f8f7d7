<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router, useForm } from '@inertiajs/vue3';
import { Plus, Package, TrendingUp, TrendingDown, Edit, Trash2, Eye } from 'lucide-vue-next';
import { ref } from 'vue';

interface Product {
    id: number;
    name: string;
    quantity: number;
    created_at: string;
    updated_at: string;
}

interface Props {
    products: {
        data: Product[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const showAddStockModal = ref(false);
const showRemoveStockModal = ref(false);
const selectedProduct = ref<Product | null>(null);

const addStockForm = useForm({
    quantity: 1,
    notes: ''
});

const removeStockForm = useForm({
    quantity: 1,
    notes: ''
});

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const deleteProduct = (id: number) => {
    if (confirm('Are you sure you want to delete this product?')) {
        router.delete(route('product.destroy', id));
    }
};

const openAddStockModal = (product: Product) => {
    selectedProduct.value = product;
    addStockForm.reset();
    showAddStockModal.value = true;
};

const openRemoveStockModal = (product: Product) => {
    selectedProduct.value = product;
    removeStockForm.reset();
    showRemoveStockModal.value = true;
};

const addStock = () => {
    if (!selectedProduct.value) return;
    
    addStockForm.post(route('product.add-stock', selectedProduct.value.id), {
        onSuccess: () => {
            showAddStockModal.value = false;
            addStockForm.reset();
        }
    });
};

const removeStock = () => {
    if (!selectedProduct.value) return;
    
    removeStockForm.post(route('product.remove-stock', selectedProduct.value.id), {
        onSuccess: () => {
            showRemoveStockModal.value = false;
            removeStockForm.reset();
        }
    });
};
</script>

<template>
    <Head title="Products" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Products</h1>
                    <p class="text-muted-foreground">
                        Manage your product inventory
                    </p>
                </div>
                <Button as-child>
                    <Link :href="route('product.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        Add Product
                    </Link>
                </Button>
            </div>

            <div class="grid gap-4">
                <Card v-for="product in products.data" :key="product.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    <Package class="h-5 w-5" />
                                    {{ product.name }}
                                </CardTitle>
                                <CardDescription>
                                    Created on {{ formatDate(product.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="text-right mr-4">
                                    <p class="text-2xl font-bold">{{ product.quantity }}</p>
                                    <p class="text-sm text-muted-foreground">units in stock</p>
                                </div>
                                <Button size="sm" variant="outline" @click="openAddStockModal(product)">
                                    <TrendingUp class="h-4 w-4 mr-1" />
                                    Add Stock
                                </Button>
                                <Button 
                                    size="sm" 
                                    variant="outline" 
                                    @click="openRemoveStockModal(product)"
                                    :disabled="product.quantity === 0"
                                >
                                    <TrendingDown class="h-4 w-4 mr-1" />
                                    Remove Stock
                                </Button>
                                <Button size="sm" variant="outline" as-child>
                                    <Link :href="route('product.show', product.id)">
                                        <Eye class="h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button size="sm" variant="outline" as-child>
                                    <Link :href="route('product.edit', product.id)">
                                        <Edit class="h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button size="sm" variant="destructive" @click="deleteProduct(product.id)">
                                    <Trash2 class="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                </Card>

                <div v-if="products.data.length === 0" class="text-center py-12">
                    <Package class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No products</h3>
                    <p class="mt-1 text-sm text-muted-foreground">Get started by creating your first product.</p>
                    <div class="mt-6">
                        <Button as-child>
                            <Link :href="route('product.create')">
                                <Plus class="mr-2 h-4 w-4" />
                                Add Product
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Stock Modal -->
        <div v-if="showAddStockModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-medium mb-4">Add Stock - {{ selectedProduct?.name }}</h3>
                <form @submit.prevent="addStock" class="space-y-4">
                    <div>
                        <Label for="add_quantity">Quantity</Label>
                        <Input
                            id="add_quantity"
                            v-model.number="addStockForm.quantity"
                            type="number"
                            min="1"
                            required
                        />
                    </div>
                    <div>
                        <Label for="add_notes">Notes (Optional)</Label>
                        <textarea
                            id="add_notes"
                            v-model="addStockForm.notes"
                            class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                            placeholder="Reason for adding stock..."
                        ></textarea>
                    </div>
                    <div class="flex space-x-3">
                        <Button type="submit" :disabled="addStockForm.processing" class="flex-1">
                            {{ addStockForm.processing ? 'Adding...' : 'Add Stock' }}
                        </Button>
                        <Button type="button" variant="outline" @click="showAddStockModal = false">
                            Cancel
                        </Button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Remove Stock Modal -->
        <div v-if="showRemoveStockModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-medium mb-4">Remove Stock - {{ selectedProduct?.name }}</h3>
                <p class="text-sm text-muted-foreground mb-4">
                    Available: {{ selectedProduct?.quantity }} units
                </p>
                <form @submit.prevent="removeStock" class="space-y-4">
                    <div>
                        <Label for="remove_quantity">Quantity</Label>
                        <Input
                            id="remove_quantity"
                            v-model.number="removeStockForm.quantity"
                            type="number"
                            min="1"
                            :max="selectedProduct?.quantity"
                            required
                        />
                    </div>
                    <div>
                        <Label for="remove_notes">Notes (Optional)</Label>
                        <textarea
                            id="remove_notes"
                            v-model="removeStockForm.notes"
                            class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                            placeholder="Reason for removing stock..."
                        ></textarea>
                    </div>
                    <div class="flex space-x-3">
                        <Button type="submit" :disabled="removeStockForm.processing" variant="destructive" class="flex-1">
                            {{ removeStockForm.processing ? 'Removing...' : 'Remove Stock' }}
                        </Button>
                        <Button type="button" variant="outline" @click="showRemoveStockModal = false">
                            Cancel
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>
