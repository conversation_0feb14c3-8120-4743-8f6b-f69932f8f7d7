<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    // Check if products table exists and has data
    echo "Checking products table...\n";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
        $count = $stmt->fetch()['count'];
        echo "✅ Products table exists with $count products\n";
        
        if ($count == 0) {
            echo "Creating sample products...\n";
            $pdo->exec("INSERT INTO `products` (`name`, `quantity`, `created_at`, `updated_at`) VALUES 
                ('Apple', 0, NOW(), NOW()),
                ('Banana', 0, NOW(), NOW()),
                ('Mango', 0, NOW(), NOW())");
            echo "✅ Sample products created\n";
        }
        
        // Show current products
        $products = $pdo->query("SELECT * FROM products")->fetchAll();
        echo "\n📦 Current Products:\n";
        foreach ($products as $product) {
            echo "- {$product['name']}: {$product['quantity']} units (ID: {$product['id']})\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Products table doesn't exist. Creating it...\n";
        $pdo->exec("CREATE TABLE IF NOT EXISTS `products` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT '0',
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        $pdo->exec("INSERT INTO `products` (`name`, `quantity`, `created_at`, `updated_at`) VALUES 
            ('Apple', 0, NOW(), NOW()),
            ('Banana', 0, NOW(), NOW()),
            ('Mango', 0, NOW(), NOW())");
        echo "✅ Products table and sample data created\n";
    }
    
    // Check stock_movements table
    echo "\nChecking stock_movements table...\n";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM stock_movements");
        $count = $stmt->fetch()['count'];
        echo "✅ Stock movements table exists with $count movements\n";
    } catch (Exception $e) {
        echo "❌ Stock movements table doesn't exist. Creating it...\n";
        $pdo->exec("CREATE TABLE IF NOT EXISTS `stock_movements` (
            `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            `product_id` bigint(20) unsigned NOT NULL,
            `user_id` bigint(20) unsigned NOT NULL,
            `type` enum('in','out') NOT NULL,
            `quantity` int(11) NOT NULL,
            `reference_type` varchar(255) NULL,
            `reference_id` bigint(20) unsigned NULL,
            `notes` text NULL,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `stock_movements_product_id_foreign` (`product_id`),
            KEY `stock_movements_user_id_foreign` (`user_id`),
            CONSTRAINT `stock_movements_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
            CONSTRAINT `stock_movements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "✅ Stock movements table created\n";
    }
    
    // Get admin user
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "❌ Admin user not found. Creating admin user...\n";
        $pdo->exec("INSERT INTO users (name, email, password, role, email_verified_at, created_at, updated_at) VALUES 
            ('Admin User', '<EMAIL>', '" . password_hash('password', PASSWORD_DEFAULT) . "', 'admin', NOW(), NOW(), NOW())");
        
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $admin = $stmt->fetch();
        echo "✅ Admin user created\n";
    }
    
    // Create initial stock movements if they don't exist
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM stock_movements");
    $movementCount = $stmt->fetch()['count'];
    
    if ($movementCount == 0) {
        echo "\nCreating initial stock movements...\n";
        $products = $pdo->query("SELECT * FROM products")->fetchAll();
        
        foreach ($products as $product) {
            // Create initial stock in
            $pdo->exec("INSERT INTO stock_movements (product_id, user_id, type, quantity, reference_type, notes, created_at, updated_at) VALUES 
                ({$product['id']}, {$admin['id']}, 'in', 500, 'initial', 'Initial stock for {$product['name']}', NOW(), NOW())");
            
            // Create sample stock out
            $pdo->exec("INSERT INTO stock_movements (product_id, user_id, type, quantity, reference_type, notes, created_at, updated_at) VALUES 
                ({$product['id']}, {$admin['id']}, 'out', 50, 'manual', 'Sample stock removal for {$product['name']}', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY))");
            
            // Update product quantity
            $pdo->exec("UPDATE products SET quantity = 450 WHERE id = {$product['id']}");
            
            echo "✅ Created stock movements for {$product['name']}\n";
        }
    }
    
    // Final status
    echo "\n🎉 Database setup complete!\n";
    echo "\n📊 Final Status:\n";
    $products = $pdo->query("SELECT * FROM products")->fetchAll();
    foreach ($products as $product) {
        echo "- {$product['name']}: {$product['quantity']} units\n";
    }
    
    $movementCount = $pdo->query("SELECT COUNT(*) as count FROM stock_movements")->fetch()['count'];
    echo "\n📈 Total stock movements: $movementCount\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
