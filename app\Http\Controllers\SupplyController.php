<?php

namespace App\Http\Controllers;

use App\Models\PoRecieved;
use App\Models\Product;
use App\Models\StockMovement;
use App\Models\Supply;
use App\Models\SupplyItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class SupplyController extends Controller
{
    /**
     * Display pending POs for supply
     */
    public function index(): Response
    {
        $query = PoRecieved::with(['user:id,name', 'items'])
            ->whereIn('status', ['pending', 'partially_supplied']);

        // If user is not admin, only show their own POs
        if (auth()->user()->role !== 'admin') {
            $query->where('user_id', auth()->id());
        }

        $pendingPOs = $query->orderBy('created_at', 'desc')->paginate(10);

        return Inertia::render('Supply/Index', [
            'pendingPOs' => $pendingPOs,
        ]);
    }

    /**
     * Show supply form for a specific PO
     */
    public function show($id): Response
    {
        $po = PoRecieved::with(['user:id,name', 'items'])->findOrFail($id);

        // Check if user can supply this PO
        if (auth()->user()->role !== 'admin' && $po->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to this PO.');
        }

        $products = Product::orderBy('name')->get();

        // Get already supplied quantities
        $suppliedQuantities = [];
        foreach ($po->items as $item) {
            $supplied = SupplyItem::whereHas('supply', function ($query) use ($po) {
                $query->where('po_recieved_id', $po->id);
            })->where('po_recieved_item_id', $item->id)->sum('quantity_supplied');

            $suppliedQuantities[$item->id] = $supplied;
        }

        return Inertia::render('Supply/Show', [
            'po' => $po,
            'products' => $products,
            'suppliedQuantities' => $suppliedQuantities,
        ]);
    }

    /**
     * Process supply for a PO
     */
    public function store(Request $request, $id)
    {
        $po = PoRecieved::with('items')->findOrFail($id);

        // Check if user can supply this PO
        if (auth()->user()->role !== 'admin' && $po->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to this PO.');
        }

        $request->validate([
            'supplies' => 'required|array|min:1',
            'supplies.*.po_item_id' => 'required|exists:po_recieved_items,id',
            'supplies.*.quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        DB::transaction(function () use ($request, $po) {
            // Create supply record
            $supply = Supply::create([
                'po_recieved_id' => $po->id,
                'user_id' => auth()->id(),
                'status' => 'pending',
                'notes' => $request->notes,
            ]);

            $hasValidSupplies = false;

            foreach ($request->supplies as $supplyData) {
                if ($supplyData['quantity'] <= 0) {
                    continue; // Skip zero quantities
                }

                $poItem = $po->items()->findOrFail($supplyData['po_item_id']);

                // Find matching product (exact name match first, then partial)
                $product = Product::where('name', $poItem->product_name)->first();
                if (!$product) {
                    $product = Product::where('name', 'LIKE', '%' . $poItem->product_name . '%')->first();
                }

                if (!$product) {
                    // Log the issue for debugging
                    \Log::warning("No product found for PO item: " . $poItem->product_name);
                    continue; // Skip this item
                }

                if ($product->quantity >= $supplyData['quantity']) {
                    // Create supply item
                    SupplyItem::create([
                        'supply_id' => $supply->id,
                        'po_recieved_item_id' => $supplyData['po_item_id'],
                        'quantity_supplied' => $supplyData['quantity'],
                    ]);

                    // Create stock movement (out)
                    StockMovement::create([
                        'product_id' => $product->id,
                        'user_id' => auth()->id(),
                        'type' => 'out',
                        'quantity' => $supplyData['quantity'],
                        'reference_type' => 'supply',
                        'reference_id' => $supply->id,
                        'notes' => "Supply for PO #{$po->po_number} - {$poItem->product_name}",
                    ]);

                    $hasValidSupplies = true;
                }
            }

            if ($hasValidSupplies) {
                // Update supply and PO status
                $supply->updatePoStatus();
            } else {
                // Delete the supply if no valid supplies were processed
                $supply->delete();
                throw new \Exception('No valid supplies could be processed. Check product availability.');
            }
        });

        return redirect()->route('supply.index')
            ->with('success', 'Supply processed successfully.')
            ->with('supply_id', $supply->id);
    }

    /**
     * Get the latest supply for a PO (API endpoint)
     */
    public function getLatestSupply($poId)
    {
        $supply = Supply::where('po_recieved_id', $poId)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$supply) {
            return response()->json([
                'success' => false,
                'message' => 'No supply found for this PO'
            ]);
        }

        return response()->json([
            'success' => true,
            'supply_id' => $supply->id
        ]);
    }
}
