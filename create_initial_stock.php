<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    // Get admin user
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "❌ Admin user not found. Please run verify_database.php first.\n";
        exit;
    }
    
    // Check if stock movements already exist
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM stock_movements");
    $count = $stmt->fetch()['count'];
    
    if ($count > 0) {
        echo "ℹ️  Stock movements already exist ($count records found).\n";
        echo "✅ Stock pages should now show data!\n";
        exit;
    }
    
    echo "Creating initial stock movements...\n";
    
    // Get all products
    $products = $pdo->query("SELECT * FROM products")->fetchAll();
    
    if (empty($products)) {
        echo "❌ No products found. Please run setup_database.php first.\n";
        exit;
    }
    
    foreach ($products as $product) {
        // Create initial stock in movement
        $stmt = $pdo->prepare("
            INSERT INTO stock_movements (product_id, user_id, type, quantity, reference_type, notes, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([
            $product['id'],
            $admin['id'],
            'in',
            500,
            'initial',
            'Initial stock for ' . $product['name']
        ]);
        
        echo "✅ Created initial stock movement for {$product['name']}: +500 units\n";
        
        // Create some sample stock out movements
        $stmt = $pdo->prepare("
            INSERT INTO stock_movements (product_id, user_id, type, quantity, reference_type, notes, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY))
        ");
        $stmt->execute([
            $product['id'],
            $admin['id'],
            'out',
            50,
            'manual',
            'Sample stock removal for ' . $product['name']
        ]);
        
        echo "✅ Created sample stock out movement for {$product['name']}: -50 units\n";
    }
    
    // Update product quantities
    echo "\nUpdating product quantities...\n";
    foreach ($products as $product) {
        $stmt = $pdo->prepare("
            UPDATE products 
            SET quantity = (
                SELECT COALESCE(
                    (SELECT SUM(quantity) FROM stock_movements WHERE product_id = ? AND type = 'in') - 
                    (SELECT SUM(quantity) FROM stock_movements WHERE product_id = ? AND type = 'out'), 
                    0
                )
            ) 
            WHERE id = ?
        ");
        $stmt->execute([$product['id'], $product['id'], $product['id']]);
        
        // Get updated quantity
        $stmt = $pdo->prepare("SELECT quantity FROM products WHERE id = ?");
        $stmt->execute([$product['id']]);
        $updatedProduct = $stmt->fetch();
        
        echo "✅ Updated {$product['name']} quantity to {$updatedProduct['quantity']} units\n";
    }
    
    echo "\n🎉 Initial stock movements created successfully!\n";
    echo "\nNow you can:\n";
    echo "1. Visit Stock In page to see incoming movements\n";
    echo "2. Visit Stock Out page to see outgoing movements\n";
    echo "3. Create transfers and returns to see more movements\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
