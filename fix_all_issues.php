<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    // Step 1: Clear all dummy data
    echo "Step 1: Clearing all dummy data...\n";
    $pdo->exec("DELETE FROM stock_movements");
    $pdo->exec("DELETE FROM supply_items");
    $pdo->exec("DELETE FROM supplies");
    $pdo->exec("DELETE FROM delivery_challans");
    $pdo->exec("DELETE FROM invoices");
    $pdo->exec("DELETE FROM transfers");
    $pdo->exec("DELETE FROM returns");
    $pdo->exec("DELETE FROM payments");
    $pdo->exec("UPDATE products SET quantity = 0");
    $pdo->exec("UPDATE po_recieved SET status = 'pending'");
    echo "✅ All dummy data cleared\n";
    
    // Step 2: Ensure all tables exist
    echo "\nStep 2: Creating/verifying all tables...\n";
    
    // Products table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `products` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `quantity` int(11) NOT NULL DEFAULT '0',
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Products table verified\n";
    
    // Stock movements table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `stock_movements` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `product_id` bigint(20) unsigned NOT NULL,
        `user_id` bigint(20) unsigned NOT NULL,
        `type` enum('in','out') NOT NULL,
        `quantity` int(11) NOT NULL,
        `reference_type` varchar(255) NULL,
        `reference_id` bigint(20) unsigned NULL,
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `stock_movements_product_id_foreign` (`product_id`),
        KEY `stock_movements_user_id_foreign` (`user_id`),
        CONSTRAINT `stock_movements_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
        CONSTRAINT `stock_movements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Stock movements table verified\n";
    
    // Supplies table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `supplies` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `po_recieved_id` bigint(20) unsigned NOT NULL,
        `user_id` bigint(20) unsigned NOT NULL,
        `status` enum('pending','partial','completed') NOT NULL DEFAULT 'pending',
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `supplies_po_recieved_id_foreign` (`po_recieved_id`),
        KEY `supplies_user_id_foreign` (`user_id`),
        CONSTRAINT `supplies_po_recieved_id_foreign` FOREIGN KEY (`po_recieved_id`) REFERENCES `po_recieved` (`id`) ON DELETE CASCADE,
        CONSTRAINT `supplies_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Supplies table verified\n";
    
    // Supply items table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `supply_items` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `supply_id` bigint(20) unsigned NOT NULL,
        `po_recieved_item_id` bigint(20) unsigned NOT NULL,
        `quantity_supplied` int(11) NOT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `supply_items_supply_id_foreign` (`supply_id`),
        KEY `supply_items_po_recieved_item_id_foreign` (`po_recieved_item_id`),
        CONSTRAINT `supply_items_supply_id_foreign` FOREIGN KEY (`supply_id`) REFERENCES `supplies` (`id`) ON DELETE CASCADE,
        CONSTRAINT `supply_items_po_recieved_item_id_foreign` FOREIGN KEY (`po_recieved_item_id`) REFERENCES `po_recieved_items` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Supply items table verified\n";
    
    // Delivery challans table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `delivery_challans` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `supply_id` bigint(20) unsigned NOT NULL,
        `dc_number` varchar(255) NOT NULL UNIQUE,
        `dc_date` date NOT NULL,
        `total_amount` decimal(10,2) NOT NULL,
        `notes` text NULL,
        `dc_proof_image` varchar(255) NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `delivery_challans_supply_id_foreign` (`supply_id`),
        CONSTRAINT `delivery_challans_supply_id_foreign` FOREIGN KEY (`supply_id`) REFERENCES `supplies` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Delivery challans table verified\n";
    
    // Invoices table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `invoices` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `supply_id` bigint(20) unsigned NOT NULL,
        `invoice_number` varchar(255) NOT NULL UNIQUE,
        `invoice_date` date NOT NULL,
        `subtotal` decimal(10,2) NOT NULL,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
        `total_amount` decimal(10,2) NOT NULL,
        `notes` text NULL,
        `invoice_proof_image` varchar(255) NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `invoices_supply_id_foreign` (`supply_id`),
        CONSTRAINT `invoices_supply_id_foreign` FOREIGN KEY (`supply_id`) REFERENCES `supplies` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Invoices table verified\n";
    
    // Transfers table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `transfers` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `from_user_id` bigint(20) unsigned NOT NULL,
        `to_user_id` bigint(20) unsigned NOT NULL,
        `product_id` bigint(20) unsigned NOT NULL,
        `quantity` int(11) NOT NULL,
        `status` enum('pending','completed') NOT NULL DEFAULT 'pending',
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `transfers_from_user_id_foreign` (`from_user_id`),
        KEY `transfers_to_user_id_foreign` (`to_user_id`),
        KEY `transfers_product_id_foreign` (`product_id`),
        CONSTRAINT `transfers_from_user_id_foreign` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `transfers_to_user_id_foreign` FOREIGN KEY (`to_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `transfers_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Transfers table verified\n";
    
    // Returns table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `returns` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `user_id` bigint(20) unsigned NOT NULL,
        `product_id` bigint(20) unsigned NOT NULL,
        `quantity` int(11) NOT NULL,
        `price` decimal(10,2) NOT NULL,
        `reason` text NOT NULL,
        `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `returns_user_id_foreign` (`user_id`),
        KEY `returns_product_id_foreign` (`product_id`),
        CONSTRAINT `returns_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `returns_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Returns table verified\n";
    
    // Payments table
    $pdo->exec("CREATE TABLE IF NOT EXISTS `payments` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `po_recieved_id` bigint(20) unsigned NOT NULL,
        `user_id` bigint(20) unsigned NOT NULL,
        `cheque_number` varchar(255) NOT NULL,
        `cheque_image` varchar(255) NULL,
        `amount` decimal(10,2) NOT NULL,
        `payment_date` date NOT NULL,
        `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `payments_po_recieved_id_foreign` (`po_recieved_id`),
        KEY `payments_user_id_foreign` (`user_id`),
        CONSTRAINT `payments_po_recieved_id_foreign` FOREIGN KEY (`po_recieved_id`) REFERENCES `po_recieved` (`id`) ON DELETE CASCADE,
        CONSTRAINT `payments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Payments table verified\n";
    
    echo "\n🎉 All issues fixed!\n";
    echo "\n📋 Summary of fixes:\n";
    echo "✅ Removed Stock In from navigation\n";
    echo "✅ Renamed Products to Stock\n";
    echo "✅ Fixed transfer validation\n";
    echo "✅ Created Payment pages\n";
    echo "✅ Fixed DC/Invoice generation\n";
    echo "✅ All tables verified and ready\n";
    
    echo "\n🚀 Next Steps:\n";
    echo "1. Create storage link: php artisan storage:link\n";
    echo "2. Start application: php artisan serve\n";
    echo "3. Start frontend: npm run dev\n";
    echo "4. Test all functionality!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
