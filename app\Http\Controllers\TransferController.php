<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Transfer;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class TransferController extends Controller
{
    /**
     * Display transfers
     */
    public function index(): Response
    {
        $transfers = Transfer::with(['fromUser:id,name', 'toUser:id,name', 'product'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Transfer/Index', [
            'transfers' => $transfers,
        ]);
    }

    /**
     * Show transfer form
     */
    public function create(): Response
    {
        $products = Product::where('quantity', '>', 0)->orderBy('name')->get();
        $users = User::where('id', '!=', auth()->id())->orderBy('name')->get();

        return Inertia::render('Transfer/Create', [
            'products' => $products,
            'users' => $users,
        ]);
    }

    /**
     * Store a new transfer
     */
    public function store(Request $request)
    {
        $request->validate([
            'to_user_id' => 'required|exists:users,id|different:from_user_id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        $product = Product::find($request->product_id);
        
        if ($product->quantity < $request->quantity) {
            return redirect()->back()->with('error', 'Insufficient stock available for transfer.');
        }

        $transfer = Transfer::create([
            'from_user_id' => auth()->id(),
            'to_user_id' => $request->to_user_id,
            'product_id' => $request->product_id,
            'quantity' => $request->quantity,
            'notes' => $request->notes,
        ]);

        // Complete the transfer immediately
        $transfer->complete();

        return redirect()->route('transfer.index')
            ->with('success', 'Transfer completed successfully.');
    }

    /**
     * Show transfer details
     */
    public function show($id): Response
    {
        $transfer = Transfer::with(['fromUser:id,name', 'toUser:id,name', 'product'])
            ->findOrFail($id);

        return Inertia::render('Transfer/Show', [
            'transfer' => $transfer,
        ]);
    }
}
