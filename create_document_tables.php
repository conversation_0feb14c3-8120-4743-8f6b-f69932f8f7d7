<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    // Create delivery_challans table
    echo "Creating delivery_challans table...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS `delivery_challans` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `supply_id` bigint(20) unsigned NOT NULL,
        `dc_number` varchar(255) NOT NULL UNIQUE,
        `dc_date` date NOT NULL,
        `total_amount` decimal(10,2) NOT NULL,
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `delivery_challans_supply_id_foreign` (`supply_id`),
        CONSTRAINT `delivery_challans_supply_id_foreign` FOREIGN KEY (`supply_id`) REFERENCES `supplies` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Delivery challans table created\n";
    
    // Create invoices table
    echo "Creating invoices table...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS `invoices` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `supply_id` bigint(20) unsigned NOT NULL,
        `invoice_number` varchar(255) NOT NULL UNIQUE,
        `invoice_date` date NOT NULL,
        `subtotal` decimal(10,2) NOT NULL,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
        `total_amount` decimal(10,2) NOT NULL,
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `invoices_supply_id_foreign` (`supply_id`),
        CONSTRAINT `invoices_supply_id_foreign` FOREIGN KEY (`supply_id`) REFERENCES `supplies` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Invoices table created\n";
    
    // Update migration records
    echo "Updating migration records...\n";
    $pdo->exec("INSERT IGNORE INTO `migrations` (`migration`, `batch`) VALUES 
        ('2024_01_01_000015_create_delivery_challans_table', 4),
        ('2024_01_01_000016_create_invoices_table', 4)");
    echo "✅ Migration records updated\n";
    
    echo "\n🎉 Document tables created successfully!\n";
    echo "\nNew features available:\n";
    echo "- Generate DC (Delivery Challan) when processing supplies\n";
    echo "- Generate Invoice with tax calculations\n";
    echo "- Print-friendly PDF views\n";
    echo "- Document management system\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
