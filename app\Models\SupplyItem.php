<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupplyItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'supply_id',
        'po_recieved_item_id',
        'quantity_supplied',
    ];

    protected $casts = [
        'quantity_supplied' => 'integer',
    ];

    /**
     * Get the supply for this item
     */
    public function supply(): BelongsTo
    {
        return $this->belongsTo(Supply::class);
    }

    /**
     * Get the PO item for this supply item
     */
    public function poRecievedItem(): BelongsTo
    {
        return $this->belongsTo(PoRecievedItem::class);
    }

    /**
     * Update supply and PO status after saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($supplyItem) {
            $supplyItem->supply->updatePoStatus();
        });

        static::deleted(function ($supplyItem) {
            $supplyItem->supply->updatePoStatus();
        });
    }
}
