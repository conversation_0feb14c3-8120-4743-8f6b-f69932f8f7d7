<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    echo "🧹 Clearing ALL stock data...\n";
    
    // Step 1: Clear all stock-related data
    echo "Step 1: Clearing stock movements...\n";
    $pdo->exec("DELETE FROM stock_movements");
    echo "✅ Stock movements cleared\n";
    
    echo "Step 2: Clearing supply data...\n";
    $pdo->exec("DELETE FROM supply_items");
    $pdo->exec("DELETE FROM supplies");
    echo "✅ Supply data cleared\n";
    
    echo "Step 3: Clearing document data...\n";
    $pdo->exec("DELETE FROM delivery_challans");
    $pdo->exec("DELETE FROM invoices");
    echo "✅ Document data cleared\n";
    
    echo "Step 4: Clearing transfer data...\n";
    $pdo->exec("DELETE FROM transfers");
    echo "✅ Transfer data cleared\n";
    
    echo "Step 5: Clearing return data...\n";
    $pdo->exec("DELETE FROM returns");
    echo "✅ Return data cleared\n";
    
    echo "Step 6: Clearing payment data...\n";
    $pdo->exec("DELETE FROM payments");
    echo "✅ Payment data cleared\n";
    
    echo "Step 7: Clearing all products...\n";
    $pdo->exec("DELETE FROM products");
    echo "✅ All products deleted\n";
    
    echo "Step 8: Resetting PO status...\n";
    $pdo->exec("UPDATE po_recieved SET status = 'pending'");
    echo "✅ PO status reset to pending\n";
    
    // Step 9: Reset auto-increment IDs
    echo "Step 9: Resetting auto-increment IDs...\n";
    $tables = [
        'products', 'stock_movements', 'supplies', 'supply_items',
        'delivery_challans', 'invoices', 'transfers', 'returns', 'payments'
    ];
    
    foreach ($tables as $table) {
        $pdo->exec("ALTER TABLE `$table` AUTO_INCREMENT = 1");
        echo "✅ Reset $table auto-increment\n";
    }
    
    echo "\n🎉 All stock data cleared successfully!\n";
    echo "\n📋 What was cleared:\n";
    echo "✅ All products deleted\n";
    echo "✅ All stock movements deleted\n";
    echo "✅ All supply records deleted\n";
    echo "✅ All documents (DC/Invoice) deleted\n";
    echo "✅ All transfers deleted\n";
    echo "✅ All returns deleted\n";
    echo "✅ All payments deleted\n";
    echo "✅ PO status reset to pending\n";
    echo "✅ Auto-increment IDs reset\n";
    
    echo "\n🚀 Next Steps:\n";
    echo "1. Start the application: php artisan serve\n";
    echo "2. Start frontend: npm run dev\n";
    echo "3. Login and add products through the UI\n";
    echo "4. Add stock through product management\n";
    echo "5. Test the complete workflow\n";
    
    echo "\n📝 How to add products:\n";
    echo "1. Go to 'Stock' in the navigation\n";
    echo "2. Click 'Add Product'\n";
    echo "3. Enter product name and initial quantity\n";
    echo "4. Save the product\n";
    echo "5. Use 'Add Stock' and 'Remove Stock' buttons to manage inventory\n";
    
    echo "\n👥 Login Credentials:\n";
    echo "Admin: <EMAIL> / password\n";
    echo "Users: <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
