<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { ArrowLeft, CreditCard, Package, User, Calendar, DollarSign } from 'lucide-vue-next';

interface Payment {
    id: number;
    cheque_number: string;
    cheque_image: string | null;
    amount: number;
    payment_date: string;
    status: 'pending' | 'approved' | 'rejected';
    notes: string | null;
    created_at: string;
    poRecieved: {
        id: number;
        po_number: string;
        institution_name: string;
        address: string;
        email: string;
        phone: string;
        total_amount: number;
        items: Array<{
            id: number;
            product_name: string;
            quantity: number;
            price: number;
            total: number;
        }>;
    };
    user: {
        id: number;
        name: string;
    };
}

interface Props {
    payment: Payment;
}

defineProps<Props>();

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'text-yellow-600 bg-yellow-100';
        case 'approved':
            return 'text-green-600 bg-green-100';
        case 'rejected':
            return 'text-red-600 bg-red-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
};
</script>

<template>
    <Head :title="`Payment - ${payment.cheque_number}`" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center space-x-4">
                <Button variant="outline" size="sm" as-child>
                    <Link :href="route('payment.index')">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back
                    </Link>
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Payment Details</h1>
                    <p class="text-muted-foreground">
                        View payment information and related PO details
                    </p>
                </div>
            </div>

            <!-- Payment Information -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <CreditCard class="h-5 w-5" />
                        Payment Information
                        <span :class="`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(payment.status)}`">
                            {{ payment.status.toUpperCase() }}
                        </span>
                    </CardTitle>
                    <CardDescription>
                        Payment recorded on {{ formatDate(payment.created_at) }}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                                <CreditCard class="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Cheque Number</p>
                                <p class="text-lg font-semibold">{{ payment.cheque_number }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
                                <DollarSign class="h-5 w-5 text-green-600" />
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Amount</p>
                                <p class="text-lg font-semibold">{{ formatCurrency(payment.amount) }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-full">
                                <Calendar class="h-5 w-5 text-purple-600" />
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Payment Date</p>
                                <p class="text-lg font-semibold">{{ formatDate(payment.payment_date) }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center justify-center w-10 h-10 bg-orange-100 rounded-full">
                                <User class="h-5 w-5 text-orange-600" />
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Recorded By</p>
                                <p class="text-lg font-semibold">{{ payment.user.name }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cheque Image -->
                    <div v-if="payment.cheque_image" class="mt-6">
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Cheque Image</h4>
                        <img 
                            :src="`/storage/${payment.cheque_image}`" 
                            alt="Cheque Image" 
                            class="max-w-md h-auto border rounded-lg shadow-sm cursor-pointer"
                            @click="window.open(`/storage/${payment.cheque_image}`, '_blank')"
                        />
                    </div>
                    
                    <!-- Notes -->
                    <div v-if="payment.notes" class="mt-6">
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Notes</h4>
                        <p class="text-gray-700">{{ payment.notes }}</p>
                    </div>
                </CardContent>
            </Card>

            <!-- Related PO Information -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Package class="h-5 w-5" />
                        Related Purchase Order
                    </CardTitle>
                    <CardDescription>
                        PO details for this payment
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="font-medium mb-2">PO Information</h4>
                            <p><strong>PO Number:</strong> {{ payment.poRecieved.po_number }}</p>
                            <p><strong>Institution:</strong> {{ payment.poRecieved.institution_name }}</p>
                            <p><strong>Total Amount:</strong> {{ formatCurrency(payment.poRecieved.total_amount) }}</p>
                        </div>
                        <div>
                            <h4 class="font-medium mb-2">Contact Information</h4>
                            <p><strong>Email:</strong> {{ payment.poRecieved.email }}</p>
                            <p><strong>Phone:</strong> {{ payment.poRecieved.phone }}</p>
                            <p><strong>Address:</strong> {{ payment.poRecieved.address }}</p>
                        </div>
                    </div>
                    
                    <!-- PO Items -->
                    <div>
                        <h4 class="font-medium mb-4">PO Items</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="item in payment.poRecieved.items" :key="item.id">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {{ item.product_name }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ item.quantity }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ formatCurrency(item.price) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {{ formatCurrency(item.total) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
