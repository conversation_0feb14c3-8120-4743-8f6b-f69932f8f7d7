<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\StockMovement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class StockController extends Controller
{
    /**
     * Display stock in movements
     */
    public function stockIn(): Response
    {
        $stockMovements = StockMovement::with(['product', 'user'])
            ->where('type', 'in')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Stock/StockIn', [
            'stockMovements' => $stockMovements,
        ]);
    }

    /**
     * Display stock out movements
     */
    public function stockOut(): Response
    {
        $stockMovements = StockMovement::with(['product', 'user'])
            ->where('type', 'out')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Stock/StockOut', [
            'stockMovements' => $stockMovements,
        ]);
    }

    /**
     * Display current stock levels
     */
    public function index(): Response
    {
        $products = Product::with(['stockMovements.user:id,name'])
            ->orderBy('name')
            ->paginate(10);

        return Inertia::render('Stock/Index', [
            'products' => $products,
        ]);
    }

    /**
     * Add stock manually
     */
    public function addStock(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        StockMovement::create([
            'product_id' => $request->product_id,
            'user_id' => auth()->id(),
            'type' => 'in',
            'quantity' => $request->quantity,
            'reference_type' => 'manual',
            'notes' => $request->notes ?? 'Manual stock addition',
        ]);

        return redirect()->back()->with('success', 'Stock added successfully.');
    }

    /**
     * Remove stock manually
     */
    public function removeStock(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        $product = Product::find($request->product_id);

        if ($product->quantity < $request->quantity) {
            return redirect()->back()->with('error', 'Insufficient stock available.');
        }

        StockMovement::create([
            'product_id' => $request->product_id,
            'user_id' => auth()->id(),
            'type' => 'out',
            'quantity' => $request->quantity,
            'reference_type' => 'manual',
            'notes' => $request->notes ?? 'Manual stock removal',
        ]);

        return redirect()->back()->with('success', 'Stock removed successfully.');
    }
}
