<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('delivery_challans', function (Blueprint $table) {
            $table->string('dc_proof_image')->nullable()->after('notes');
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->string('invoice_proof_image')->nullable()->after('notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('delivery_challans', function (Blueprint $table) {
            $table->dropColumn('dc_proof_image');
        });

        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn('invoice_proof_image');
        });
    }
};
