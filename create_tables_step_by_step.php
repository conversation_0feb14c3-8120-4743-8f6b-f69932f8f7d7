<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    // Step 1: Add status column to po_recieved table if it doesn't exist
    echo "Step 1: Adding status column to po_recieved table...\n";
    try {
        $pdo->exec("ALTER TABLE `po_recieved` ADD COLUMN `status` enum('pending','partially_supplied','supplied') NOT NULL DEFAULT 'pending' AFTER `total_amount`");
        echo "✅ Status column added to po_recieved table\n";
    } catch (Exception $e) {
        echo "⚠️  Status column might already exist: " . $e->getMessage() . "\n";
    }
    
    // Step 2: Create products table
    echo "\nStep 2: Creating products table...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS `products` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `quantity` int(11) NOT NULL DEFAULT '0',
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Products table created\n";
    
    // Step 3: Create stock_movements table
    echo "\nStep 3: Creating stock_movements table...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS `stock_movements` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `product_id` bigint(20) unsigned NOT NULL,
        `user_id` bigint(20) unsigned NOT NULL,
        `type` enum('in','out') NOT NULL,
        `quantity` int(11) NOT NULL,
        `reference_type` varchar(255) NULL,
        `reference_id` bigint(20) unsigned NULL,
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `stock_movements_product_id_foreign` (`product_id`),
        KEY `stock_movements_user_id_foreign` (`user_id`),
        CONSTRAINT `stock_movements_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
        CONSTRAINT `stock_movements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Stock movements table created\n";
    
    // Step 4: Create supplies table
    echo "\nStep 4: Creating supplies table...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS `supplies` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `po_recieved_id` bigint(20) unsigned NOT NULL,
        `user_id` bigint(20) unsigned NOT NULL,
        `status` enum('pending','partial','completed') NOT NULL DEFAULT 'pending',
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `supplies_po_recieved_id_foreign` (`po_recieved_id`),
        KEY `supplies_user_id_foreign` (`user_id`),
        CONSTRAINT `supplies_po_recieved_id_foreign` FOREIGN KEY (`po_recieved_id`) REFERENCES `po_recieved` (`id`) ON DELETE CASCADE,
        CONSTRAINT `supplies_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Supplies table created\n";
    
    // Step 5: Create supply_items table
    echo "\nStep 5: Creating supply_items table...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS `supply_items` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `supply_id` bigint(20) unsigned NOT NULL,
        `po_recieved_item_id` bigint(20) unsigned NOT NULL,
        `quantity_supplied` int(11) NOT NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `supply_items_supply_id_foreign` (`supply_id`),
        KEY `supply_items_po_recieved_item_id_foreign` (`po_recieved_item_id`),
        CONSTRAINT `supply_items_supply_id_foreign` FOREIGN KEY (`supply_id`) REFERENCES `supplies` (`id`) ON DELETE CASCADE,
        CONSTRAINT `supply_items_po_recieved_item_id_foreign` FOREIGN KEY (`po_recieved_item_id`) REFERENCES `po_recieved_items` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Supply items table created\n";
    
    // Step 6: Create transfers table
    echo "\nStep 6: Creating transfers table...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS `transfers` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `from_user_id` bigint(20) unsigned NOT NULL,
        `to_user_id` bigint(20) unsigned NOT NULL,
        `product_id` bigint(20) unsigned NOT NULL,
        `quantity` int(11) NOT NULL,
        `status` enum('pending','completed') NOT NULL DEFAULT 'pending',
        `notes` text NULL,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `transfers_from_user_id_foreign` (`from_user_id`),
        KEY `transfers_to_user_id_foreign` (`to_user_id`),
        KEY `transfers_product_id_foreign` (`product_id`),
        CONSTRAINT `transfers_from_user_id_foreign` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `transfers_to_user_id_foreign` FOREIGN KEY (`to_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `transfers_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Transfers table created\n";
    
    // Step 7: Create returns table
    echo "\nStep 7: Creating returns table...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS `returns` (
        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        `user_id` bigint(20) unsigned NOT NULL,
        `product_id` bigint(20) unsigned NOT NULL,
        `quantity` int(11) NOT NULL,
        `price` decimal(10,2) NOT NULL,
        `reason` text NOT NULL,
        `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `returns_user_id_foreign` (`user_id`),
        KEY `returns_product_id_foreign` (`product_id`),
        CONSTRAINT `returns_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `returns_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    echo "✅ Returns table created\n";
    
    // Step 8: Insert sample products
    echo "\nStep 8: Inserting sample products...\n";
    $pdo->exec("INSERT IGNORE INTO `products` (`name`, `quantity`, `created_at`, `updated_at`) VALUES 
        ('Apple', 500, NOW(), NOW()),
        ('Banana', 500, NOW(), NOW()),
        ('Mango', 500, NOW(), NOW())");
    echo "✅ Sample products inserted\n";
    
    // Step 9: Update migration records
    echo "\nStep 9: Updating migration records...\n";
    $pdo->exec("INSERT IGNORE INTO `migrations` (`migration`, `batch`) VALUES 
        ('2024_01_01_000008_create_products_table', 3),
        ('2024_01_01_000009_create_stock_movements_table', 3),
        ('2024_01_01_000010_create_supplies_table', 3),
        ('2024_01_01_000011_create_supply_items_table', 3),
        ('2024_01_01_000012_create_transfers_table', 3),
        ('2024_01_01_000013_create_returns_table', 3),
        ('2024_01_01_000014_add_status_to_po_recieved_table', 3)");
    echo "✅ Migration records updated\n";
    
    echo "\n🎉 All tables created successfully!\n";
    echo "\nNow run: php verify_database.php\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
