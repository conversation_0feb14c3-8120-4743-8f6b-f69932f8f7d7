<?php

namespace App\Http\Controllers;

use App\Models\PoReceived;
use App\Models\PoReceivedItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class PoReceivedController extends Controller
{
    /**
     * Display a listing of PO Received
     */
    public function index(): Response
    {
        $poReceived = PoReceived::with(['user:id,name', 'items'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('PoReceived/Index', [
            'poReceived' => $poReceived,
        ]);
    }

    /**
     * Show the form for creating a new PO Received
     */
    public function create(): Response
    {
        return Inertia::render('PoReceived/Create');
    }

    /**
     * Store a newly created PO Received
     */
    public function store(Request $request)
    {
        $request->validate([
            'institution_name' => 'required|string|max:255',
            'address' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit' => 'required|string|max:50',
            'items.*.price' => 'required|numeric|min:0',
        ]);

        DB::transaction(function () use ($request) {
            $poReceived = PoReceived::create([
                'user_id' => auth()->id(),
                'institution_name' => $request->institution_name,
                'address' => $request->address,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            foreach ($request->items as $item) {
                PoReceivedItem::create([
                    'po_received_id' => $poReceived->id,
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'unit' => $item['unit'],
                    'price' => $item['price'],
                ]);
            }
        });

        return redirect()->route('po-received.index')
            ->with('success', 'PO Received created successfully.');
    }

    /**
     * Display the specified PO Received
     */
    public function show(PoReceived $poReceived): Response
    {
        $poReceived->load(['user:id,name', 'items']);

        return Inertia::render('PoReceived/Show', [
            'poReceived' => $poReceived,
        ]);
    }

    /**
     * Show the form for editing the specified PO Received
     */
    public function edit(PoReceived $poReceived): Response
    {
        $poReceived->load('items');

        return Inertia::render('PoReceived/Edit', [
            'poReceived' => $poReceived,
        ]);
    }

    /**
     * Update the specified PO Received
     */
    public function update(Request $request, PoReceived $poReceived)
    {
        $request->validate([
            'institution_name' => 'required|string|max:255',
            'address' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit' => 'required|string|max:50',
            'items.*.price' => 'required|numeric|min:0',
        ]);

        DB::transaction(function () use ($request, $poReceived) {
            $poReceived->update([
                'institution_name' => $request->institution_name,
                'address' => $request->address,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            // Delete existing items
            $poReceived->items()->delete();

            // Create new items
            foreach ($request->items as $item) {
                PoReceivedItem::create([
                    'po_received_id' => $poReceived->id,
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'unit' => $item['unit'],
                    'price' => $item['price'],
                ]);
            }
        });

        return redirect()->route('po-received.index')
            ->with('success', 'PO Received updated successfully.');
    }

    /**
     * Remove the specified PO Received
     */
    public function destroy(PoReceived $poReceived)
    {
        $poReceived->delete();

        return redirect()->route('po-received.index')
            ->with('success', 'PO Received deleted successfully.');
    }
}
