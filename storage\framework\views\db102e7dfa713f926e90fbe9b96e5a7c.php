<!DOCTYPE html>
<html>
<head>
    <title>Invoice - <?php echo e($invoice->invoice_number); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 14px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .document-title {
            font-size: 20px;
            font-weight: bold;
            margin-top: 20px;
            color: #d9534f;
        }
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .info-box {
            width: 48%;
        }
        .info-box h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .total-section {
            margin-top: 20px;
            text-align: right;
        }
        .total-row {
            margin: 5px 0;
            padding: 5px 0;
        }
        .grand-total {
            font-size: 18px;
            font-weight: bold;
            border-top: 2px solid #000;
            padding-top: 10px;
        }
        .footer {
            margin-top: 40px;
            border-top: 1px solid #ccc;
            padding-top: 20px;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        .signature-box {
            width: 30%;
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 10px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="no-print" style="margin-bottom: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Print Invoice</button>
        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Close</button>
    </div>

    <div class="header">
        <div class="company-name"><?php echo e(config('app.name', 'Your Company Name')); ?></div>
        <div>123 Business Street, City, State 12345</div>
        <div>Phone: (************* | Email: <EMAIL></div>
        <div>GST No: 22AAAAA0000A1Z5</div>
        <div class="document-title">TAX INVOICE</div>
    </div>

    <div class="info-section" style="display: flex;">
        <div class="info-box">
            <h3>Invoice Details</h3>
            <p><strong>Invoice Number:</strong> <?php echo e($invoice->invoice_number); ?></p>
            <p><strong>Invoice Date:</strong> <?php echo e($invoice->invoice_date->format('d/m/Y')); ?></p>
            <p><strong>PO Number:</strong> <?php echo e($invoice->supply->poRecieved->po_number); ?></p>
            <p><strong>PO Date:</strong> <?php echo e($invoice->supply->poRecieved->po_date->format('d/m/Y')); ?></p>
        </div>
        <div class="info-box">
            <h3>Bill To</h3>
            <p><strong><?php echo e($invoice->supply->poRecieved->institution_name); ?></strong></p>
            <p><?php echo e($invoice->supply->poRecieved->address); ?></p>
            <p>Email: <?php echo e($invoice->supply->poRecieved->email); ?></p>
            <p>Phone: <?php echo e($invoice->supply->poRecieved->phone); ?></p>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>S.No.</th>
                <th>Product Name</th>
                <th>Quantity</th>
                <th>Rate</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $invoice->supply->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($index + 1); ?></td>
                <td><?php echo e($item->poRecievedItem->product_name); ?></td>
                <td class="text-right"><?php echo e($item->quantity_supplied); ?></td>
                <td class="text-right">$<?php echo e(number_format($item->poRecievedItem->price, 2)); ?></td>
                <td class="text-right">$<?php echo e(number_format($item->quantity_supplied * $item->poRecievedItem->price, 2)); ?></td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>

    <div class="total-section">
        <div class="total-row">
            <strong>Subtotal: $<?php echo e(number_format($invoice->subtotal, 2)); ?></strong>
        </div>
        <div class="total-row">
            <strong>GST (18%): $<?php echo e(number_format($invoice->tax_amount, 2)); ?></strong>
        </div>
        <div class="total-row grand-total">
            <strong>Grand Total: $<?php echo e(number_format($invoice->total_amount, 2)); ?></strong>
        </div>
    </div>

    <?php if($invoice->notes): ?>
    <div style="margin-top: 20px;">
        <h3>Notes:</h3>
        <p><?php echo e($invoice->notes); ?></p>
    </div>
    <?php endif; ?>

    <div style="margin-top: 30px;">
        <h3>Terms & Conditions:</h3>
        <ul style="font-size: 12px;">
            <li>Payment is due within 30 days of invoice date.</li>
            <li>Late payments may incur additional charges.</li>
            <li>Goods once sold will not be taken back.</li>
            <li>Subject to local jurisdiction only.</li>
        </ul>
    </div>

    <div class="signature-section" style="display: flex;">
        <div class="signature-box">
            <div>Prepared By</div>
            <div style="margin-top: 20px;"><?php echo e($invoice->supply->user->name); ?></div>
        </div>
        <div class="signature-box">
            <div>Authorized Signatory</div>
        </div>
        <div class="signature-box">
            <div>Customer Signature</div>
        </div>
    </div>

    <div class="footer">
        <p style="text-align: center; font-size: 12px; color: #666;">
            This is a computer generated invoice. No signature required.
        </p>
    </div>
</body>
</html>
<?php /**PATH F:\xampp\htdocs\order_system\resources\views/documents/invoice.blade.php ENDPATH**/ ?>