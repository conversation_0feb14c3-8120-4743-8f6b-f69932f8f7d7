<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    echo "🔧 Fixing all final issues...\n";
    
    // Step 1: Clear all stock data completely
    echo "Step 1: Clearing ALL stock data...\n";
    $pdo->exec("DELETE FROM stock_movements");
    $pdo->exec("DELETE FROM supply_items");
    $pdo->exec("DELETE FROM supplies");
    $pdo->exec("DELETE FROM delivery_challans");
    $pdo->exec("DELETE FROM invoices");
    $pdo->exec("DELETE FROM transfers");
    $pdo->exec("DELETE FROM returns");
    $pdo->exec("DELETE FROM payments");
    $pdo->exec("DELETE FROM products");
    $pdo->exec("UPDATE po_recieved SET status = 'pending'");
    echo "✅ All stock data cleared\n";
    
    // Step 2: Reset auto-increment IDs
    echo "Step 2: Resetting auto-increment IDs...\n";
    $tables = [
        'products', 'stock_movements', 'supplies', 'supply_items',
        'delivery_challans', 'invoices', 'transfers', 'returns', 'payments'
    ];
    
    foreach ($tables as $table) {
        $pdo->exec("ALTER TABLE `$table` AUTO_INCREMENT = 1");
    }
    echo "✅ Auto-increment IDs reset\n";
    
    // Step 3: Verify users exist
    echo "Step 3: Verifying users...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    
    if ($userCount == 0) {
        echo "❌ No users found. Please run verify_database.php first.\n";
        exit;
    }
    echo "✅ Found $userCount users\n";
    
    // Step 4: Get admin user for initial data
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "❌ Admin user not found. Please run verify_database.php first.\n";
        exit;
    }
    echo "✅ Admin user found\n";
    
    echo "\n🎉 System reset complete!\n";
    echo "\n📋 What was done:\n";
    echo "✅ All products deleted\n";
    echo "✅ All stock movements deleted\n";
    echo "✅ All supply records deleted\n";
    echo "✅ All documents deleted\n";
    echo "✅ All transfers deleted\n";
    echo "✅ All returns deleted\n";
    echo "✅ All payments deleted\n";
    echo "✅ PO status reset to pending\n";
    echo "✅ Auto-increment IDs reset\n";
    echo "✅ Product/Show.vue page created\n";
    echo "✅ Supply authorization fixed\n";
    echo "✅ Product matching improved\n";
    
    echo "\n🚀 Next Steps:\n";
    echo "1. Start the application: php artisan serve\n";
    echo "2. Start frontend: npm run dev\n";
    echo "3. Login as admin or user\n";
    echo "4. Go to 'Stock' in navigation\n";
    echo "5. Click 'Add Product' to create products\n";
    echo "6. Add initial stock using 'Add Stock' button\n";
    echo "7. Test the complete workflow\n";
    
    echo "\n📝 How to use the system:\n";
    echo "\n1. Product Management:\n";
    echo "   - Go to 'Stock' page\n";
    echo "   - Click 'Add Product' to create new products\n";
    echo "   - Use 'Add Stock' and 'Remove Stock' buttons\n";
    echo "   - View product details and stock movements\n";
    
    echo "\n2. Supply Process:\n";
    echo "   - Create PO with products that match your inventory\n";
    echo "   - Go to 'Supply' page\n";
    echo "   - Click 'Supply' button for pending POs\n";
    echo "   - Enter quantities to supply\n";
    echo "   - Generate DC & Invoice documents\n";
    
    echo "\n3. Transfer System:\n";
    echo "   - Go to 'Transfer' page\n";
    echo "   - Select user and product\n";
    echo "   - System shows available stock\n";
    echo "   - Enter quantity within limits\n";
    
    echo "\n4. Document Management:\n";
    echo "   - Generate documents from Supply page\n";
    echo "   - View all documents in 'Documents' page\n";
    echo "   - Upload proof images\n";
    echo "   - Print professional PDFs\n";
    
    echo "\n5. Payment Tracking:\n";
    echo "   - Go to 'Payments' page\n";
    echo "   - Record payments with cheque details\n";
    echo "   - Upload cheque images\n";
    echo "   - Admin can approve/reject payments\n";
    
    echo "\n👥 Login Credentials:\n";
    echo "Admin: <EMAIL> / password\n";
    echo "Users: <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    
    echo "\n⚠️  Important Notes:\n";
    echo "- Products must exist before you can supply POs\n";
    echo "- Product names in POs should match product names in inventory\n";
    echo "- Stock must be available before transfers/supplies\n";
    echo "- Only admin or PO creator can supply that PO\n";
    echo "- Documents are generated per supply, not per PO\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
