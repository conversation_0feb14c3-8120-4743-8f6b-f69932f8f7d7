<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    // Step 1: Clear all data for fresh start
    echo "Step 1: Clearing all data for fresh start...\n";
    $pdo->exec("DELETE FROM stock_movements");
    $pdo->exec("DELETE FROM supply_items");
    $pdo->exec("DELETE FROM supplies");
    $pdo->exec("DELETE FROM delivery_challans");
    $pdo->exec("DELETE FROM invoices");
    $pdo->exec("DELETE FROM transfers");
    $pdo->exec("DELETE FROM returns");
    $pdo->exec("DELETE FROM payments");
    $pdo->exec("UPDATE products SET quantity = 0");
    $pdo->exec("UPDATE po_recieved SET status = 'pending'");
    echo "✅ All data cleared\n";
    
    // Step 2: Verify all tables exist
    echo "\nStep 2: Verifying all tables exist...\n";
    
    $tables = [
        'users', 'po_recieved', 'po_recieved_items', 'products', 
        'stock_movements', 'supplies', 'supply_items', 
        'delivery_challans', 'invoices', 'transfers', 'returns', 'payments'
    ];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ $table table exists\n";
        } else {
            echo "❌ $table table missing\n";
        }
    }
    
    // Step 3: Create sample products for testing
    echo "\nStep 3: Creating sample products...\n";
    
    $sampleProducts = [
        ['name' => 'Apple', 'quantity' => 100],
        ['name' => 'Banana', 'quantity' => 150],
        ['name' => 'Orange', 'quantity' => 200],
        ['name' => 'Mango', 'quantity' => 75],
        ['name' => 'Grapes', 'quantity' => 120]
    ];
    
    // Get admin user
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "❌ Admin user not found. Please run verify_database.php first.\n";
        exit;
    }
    
    foreach ($sampleProducts as $productData) {
        // Insert product
        $stmt = $pdo->prepare("INSERT INTO products (name, quantity, created_at, updated_at) VALUES (?, ?, NOW(), NOW())");
        $stmt->execute([$productData['name'], $productData['quantity']]);
        $productId = $pdo->lastInsertId();
        
        // Create initial stock movement
        $stmt = $pdo->prepare("INSERT INTO stock_movements (product_id, user_id, type, quantity, reference_type, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->execute([
            $productId,
            $admin['id'],
            'in',
            $productData['quantity'],
            'initial',
            'Initial stock for ' . $productData['name']
        ]);
        
        echo "✅ Created {$productData['name']} with {$productData['quantity']} units\n";
    }
    
    echo "\n🎉 Final system setup complete!\n";
    echo "\n📋 System Features:\n";
    echo "✅ Stock Management - View remaining stock with calculations\n";
    echo "✅ Transfer System - Transfer products between users with stock validation\n";
    echo "✅ DC & Invoice Generation - Generate documents for supplied/partially supplied POs\n";
    echo "✅ Payment Tracking - Record payments with cheque details\n";
    echo "✅ Complete Audit Trail - All stock movements tracked\n";
    echo "✅ User Access Control - Role-based permissions\n";
    
    echo "\n🚀 Navigation Structure:\n";
    echo "- Dashboard: Overview of system\n";
    echo "- Stock: View remaining stock levels and manage products\n";
    echo "- PO Recieved: Manage purchase orders\n";
    echo "- Stock Out: View all outgoing stock movements\n";
    echo "- Supply: Process PO supplies\n";
    echo "- Transfer: Transfer products between users\n";
    echo "- Return: Handle product returns\n";
    echo "- Documents: Manage DC and Invoices with proof uploads\n";
    echo "- Payments: Track payments with cheque details\n";
    
    echo "\n📊 Sample Data Created:\n";
    $products = $pdo->query("SELECT * FROM products")->fetchAll();
    foreach ($products as $product) {
        echo "- {$product['name']}: {$product['quantity']} units\n";
    }
    
    echo "\n🔧 Next Steps:\n";
    echo "1. Create storage link: php artisan storage:link\n";
    echo "2. Start application: php artisan serve\n";
    echo "3. Start frontend: npm run dev\n";
    echo "4. Login and test all features!\n";
    
    echo "\n👥 Login Credentials:\n";
    echo "Admin: <EMAIL> / password\n";
    echo "Users: <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
