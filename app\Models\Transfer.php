<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Transfer extends Model
{
    use HasFactory;

    protected $fillable = [
        'from_user_id',
        'to_user_id',
        'product_id',
        'quantity',
        'status',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'integer',
    ];

    /**
     * Get the user transferring from
     */
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    /**
     * Get the user transferring to
     */
    public function toUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    /**
     * Get the product being transferred
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Complete the transfer
     */
    public function complete(): void
    {
        if ($this->status === 'pending') {
            // Create stock movements
            StockMovement::create([
                'product_id' => $this->product_id,
                'user_id' => $this->from_user_id,
                'type' => 'out',
                'quantity' => $this->quantity,
                'reference_type' => 'transfer',
                'reference_id' => $this->id,
                'notes' => "Transfer to {$this->toUser->name}",
            ]);

            StockMovement::create([
                'product_id' => $this->product_id,
                'user_id' => $this->to_user_id,
                'type' => 'in',
                'quantity' => $this->quantity,
                'reference_type' => 'transfer',
                'reference_id' => $this->id,
                'notes' => "Transfer from {$this->fromUser->name}",
            ]);

            $this->status = 'completed';
            $this->save();
        }
    }
}
