<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { Edit, ArrowLeft } from 'lucide-vue-next';

interface PoReceivedItem {
    id: number;
    product_name: string;
    quantity: number;
    unit: string;
    price: number;
    total: number;
}

interface PoReceived {
    id: number;
    institution_name: string;
    address: string;
    email: string;
    phone: string;
    total_amount: number;
    created_at: string;
    user: {
        id: number;
        name: string;
    };
    items: PoReceivedItem[];
}

interface Props {
    poReceived: PoReceived;
}

defineProps<Props>();

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};
</script>

<template>
    <Head :title="`PO Received - ${poReceived.institution_name}`" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="outline" size="sm" as-child>
                        <Link :href="route('po-received.index')">
                            <ArrowLeft class="mr-2 h-4 w-4" />
                            Back
                        </Link>
                    </Button>
                    <div>
                        <h1 class="text-3xl font-bold tracking-tight">{{ poReceived.institution_name }}</h1>
                        <p class="text-muted-foreground">
                            Created by {{ poReceived.user.name }} on {{ formatDate(poReceived.created_at) }}
                        </p>
                    </div>
                </div>
                <Button as-child>
                    <Link :href="route('po-received.edit', poReceived.id)">
                        <Edit class="mr-2 h-4 w-4" />
                        Edit
                    </Link>
                </Button>
            </div>

            <!-- Institution Details -->
            <Card>
                <CardHeader>
                    <CardTitle>Institution Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-sm text-muted-foreground">Institution Name</h4>
                            <p class="text-lg">{{ poReceived.institution_name }}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-sm text-muted-foreground">Email</h4>
                            <p class="text-lg">{{ poReceived.email }}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-sm text-muted-foreground">Phone</h4>
                            <p class="text-lg">{{ poReceived.phone }}</p>
                        </div>
                        <div class="md:col-span-2">
                            <h4 class="font-medium text-sm text-muted-foreground">Address</h4>
                            <p class="text-lg">{{ poReceived.address }}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Items -->
            <Card>
                <CardHeader>
                    <CardTitle>Items</CardTitle>
                    <CardDescription>
                        {{ poReceived.items.length }} items in this order
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="space-y-4">
                        <div v-for="(item, index) in poReceived.items" :key="item.id" class="border rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Product</h4>
                                    <p class="text-lg">{{ item.product_name }}</p>
                                </div>
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Quantity</h4>
                                    <p class="text-lg">{{ item.quantity }}</p>
                                </div>
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Unit</h4>
                                    <p class="text-lg">{{ item.unit }}</p>
                                </div>
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Price</h4>
                                    <p class="text-lg">{{ formatCurrency(item.price) }}</p>
                                </div>
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Total</h4>
                                    <p class="text-lg font-semibold">{{ formatCurrency(item.total) }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border-t pt-4">
                            <div class="flex justify-end">
                                <div class="text-right">
                                    <h4 class="font-medium text-sm text-muted-foreground">Grand Total</h4>
                                    <p class="text-2xl font-bold">{{ formatCurrency(poReceived.total_amount) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
