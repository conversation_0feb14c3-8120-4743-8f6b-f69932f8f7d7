<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryChallan extends Model
{
    use HasFactory;

    protected $fillable = [
        'supply_id',
        'dc_number',
        'dc_date',
        'total_amount',
        'notes',
        'dc_proof_image',
    ];

    protected $casts = [
        'dc_date' => 'date',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the supply for this delivery challan
     */
    public function supply(): BelongsTo
    {
        return $this->belongsTo(Supply::class);
    }

    /**
     * Generate unique DC number
     */
    public static function generateDcNumber(): string
    {
        $year = date('Y');
        $month = date('m');
        $lastDc = self::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastDc ? (int)substr($lastDc->dc_number, -4) + 1 : 1;

        return 'DC' . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
