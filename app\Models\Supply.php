<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supply extends Model
{
    use HasFactory;

    protected $fillable = [
        'po_recieved_id',
        'user_id',
        'status',
        'notes',
    ];

    /**
     * Get the PO for this supply
     */
    public function poRecieved(): BelongsTo
    {
        return $this->belongsTo(PoRecieved::class);
    }

    /**
     * Get the user for this supply
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the supply items
     */
    public function items(): HasMany
    {
        return $this->hasMany(SupplyItem::class);
    }

    /**
     * Update PO status based on supply completion
     */
    public function updatePoStatus(): void
    {
        $po = $this->poRecieved;
        $totalItems = $po->items->count();
        $suppliedItems = 0;
        $partiallySuppliedItems = 0;

        foreach ($po->items as $poItem) {
            $totalSupplied = SupplyItem::whereHas('supply', function ($query) use ($po) {
                $query->where('po_recieved_id', $po->id);
            })->where('po_recieved_item_id', $poItem->id)->sum('quantity_supplied');

            if ($totalSupplied >= $poItem->quantity) {
                $suppliedItems++;
            } elseif ($totalSupplied > 0) {
                $partiallySuppliedItems++;
            }
        }

        if ($suppliedItems === $totalItems) {
            $po->status = 'supplied';
        } elseif ($suppliedItems > 0 || $partiallySuppliedItems > 0) {
            $po->status = 'partially_supplied';
        } else {
            $po->status = 'pending';
        }

        $po->save();
    }
}
