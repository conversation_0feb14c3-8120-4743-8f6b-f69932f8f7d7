<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    echo "Clearing all dummy data...\n";
    
    // Clear stock movements
    $pdo->exec("DELETE FROM stock_movements");
    echo "✅ Cleared stock movements\n";
    
    // Clear supply items
    $pdo->exec("DELETE FROM supply_items");
    echo "✅ Cleared supply items\n";
    
    // Clear supplies
    $pdo->exec("DELETE FROM supplies");
    echo "✅ Cleared supplies\n";
    
    // Clear delivery challans
    $pdo->exec("DELETE FROM delivery_challans");
    echo "✅ Cleared delivery challans\n";
    
    // Clear invoices
    $pdo->exec("DELETE FROM invoices");
    echo "✅ Cleared invoices\n";
    
    // Clear transfers
    $pdo->exec("DELETE FROM transfers");
    echo "✅ Cleared transfers\n";
    
    // Clear returns
    $pdo->exec("DELETE FROM returns");
    echo "✅ Cleared returns\n";
    
    // Reset product quantities to 0
    $pdo->exec("UPDATE products SET quantity = 0");
    echo "✅ Reset product quantities to 0\n";
    
    // Reset PO status to pending
    $pdo->exec("UPDATE po_recieved SET status = 'pending'");
    echo "✅ Reset PO status to pending\n";
    
    echo "\n🎉 All dummy data cleared successfully!\n";
    echo "\nNow you can:\n";
    echo "1. Add products through the product management interface\n";
    echo "2. Manage stock through proper CRUD operations\n";
    echo "3. Test the complete workflow from scratch\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
