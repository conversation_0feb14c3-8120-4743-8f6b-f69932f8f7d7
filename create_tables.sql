-- Create po_recieved table
CREATE TABLE IF NOT EXISTS `po_recieved` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) unsigned NOT NULL,
    `institution_name` varchar(255) NOT NULL,
    `address` text NOT NULL,
    `email` varchar(255) NOT NULL,
    `phone` varchar(255) NOT NULL,
    `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `po_recieved_user_id_foreign` (`user_id`),
    CONSTRAINT `po_recieved_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create po_recieved_items table
CREATE TABLE IF NOT EXISTS `po_recieved_items` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `po_recieved_id` bigint(20) unsigned NOT NULL,
    `product_name` varchar(255) NOT NULL,
    `quantity` int(11) NOT NULL,
    `unit` varchar(255) NOT NULL DEFAULT 'pcs',
    `price` decimal(10,2) NOT NULL,
    `total` decimal(10,2) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `po_recieved_items_po_recieved_id_foreign` (`po_recieved_id`),
    CONSTRAINT `po_recieved_items_po_recieved_id_foreign` FOREIGN KEY (`po_recieved_id`) REFERENCES `po_recieved` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert migration records
INSERT IGNORE INTO `migrations` (`migration`, `batch`) VALUES 
('2024_01_01_000005_create_po_recieved_table', 2),
('2024_01_01_000006_create_po_recieved_items_table', 2);
