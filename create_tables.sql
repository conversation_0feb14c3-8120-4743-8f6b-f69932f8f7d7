-- Create po_recieved table
CREATE TABLE IF NOT EXISTS `po_recieved` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) unsigned NOT NULL,
    `po_number` varchar(255) NOT NULL,
    `po_date` date NOT NULL,
    `po_image` varchar(255) NULL,
    `institution_name` varchar(255) NOT NULL,
    `address` text NOT NULL,
    `email` varchar(255) NOT NULL,
    `phone` varchar(255) NOT NULL,
    `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `po_recieved_po_number_unique` (`po_number`),
    KEY `po_recieved_user_id_foreign` (`user_id`),
    CONSTRAINT `po_recieved_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create po_recieved_items table
CREATE TABLE IF NOT EXISTS `po_recieved_items` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `po_recieved_id` bigint(20) unsigned NOT NULL,
    `product_name` varchar(255) NOT NULL,
    `quantity` int(11) NOT NULL,
    `price` decimal(10,2) NOT NULL,
    `total` decimal(10,2) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `po_recieved_items_po_recieved_id_foreign` (`po_recieved_id`),
    CONSTRAINT `po_recieved_items_po_recieved_id_foreign` FOREIGN KEY (`po_recieved_id`) REFERENCES `po_recieved` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add status column to po_recieved table
ALTER TABLE `po_recieved` ADD COLUMN `status` enum('pending','partially_supplied','supplied') NOT NULL DEFAULT 'pending' AFTER `total_amount`;

-- Create products table
CREATE TABLE IF NOT EXISTS `products` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT '0',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create stock_movements table
CREATE TABLE IF NOT EXISTS `stock_movements` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `product_id` bigint(20) unsigned NOT NULL,
    `user_id` bigint(20) unsigned NOT NULL,
    `type` enum('in','out') NOT NULL,
    `quantity` int(11) NOT NULL,
    `reference_type` varchar(255) NULL,
    `reference_id` bigint(20) unsigned NULL,
    `notes` text NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `stock_movements_product_id_foreign` (`product_id`),
    KEY `stock_movements_user_id_foreign` (`user_id`),
    CONSTRAINT `stock_movements_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
    CONSTRAINT `stock_movements_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create supplies table
CREATE TABLE IF NOT EXISTS `supplies` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `po_recieved_id` bigint(20) unsigned NOT NULL,
    `user_id` bigint(20) unsigned NOT NULL,
    `status` enum('pending','partial','completed') NOT NULL DEFAULT 'pending',
    `notes` text NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `supplies_po_recieved_id_foreign` (`po_recieved_id`),
    KEY `supplies_user_id_foreign` (`user_id`),
    CONSTRAINT `supplies_po_recieved_id_foreign` FOREIGN KEY (`po_recieved_id`) REFERENCES `po_recieved` (`id`) ON DELETE CASCADE,
    CONSTRAINT `supplies_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create supply_items table
CREATE TABLE IF NOT EXISTS `supply_items` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `supply_id` bigint(20) unsigned NOT NULL,
    `po_recieved_item_id` bigint(20) unsigned NOT NULL,
    `quantity_supplied` int(11) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `supply_items_supply_id_foreign` (`supply_id`),
    KEY `supply_items_po_recieved_item_id_foreign` (`po_recieved_item_id`),
    CONSTRAINT `supply_items_supply_id_foreign` FOREIGN KEY (`supply_id`) REFERENCES `supplies` (`id`) ON DELETE CASCADE,
    CONSTRAINT `supply_items_po_recieved_item_id_foreign` FOREIGN KEY (`po_recieved_item_id`) REFERENCES `po_recieved_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create transfers table
CREATE TABLE IF NOT EXISTS `transfers` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `from_user_id` bigint(20) unsigned NOT NULL,
    `to_user_id` bigint(20) unsigned NOT NULL,
    `product_id` bigint(20) unsigned NOT NULL,
    `quantity` int(11) NOT NULL,
    `status` enum('pending','completed') NOT NULL DEFAULT 'pending',
    `notes` text NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `transfers_from_user_id_foreign` (`from_user_id`),
    KEY `transfers_to_user_id_foreign` (`to_user_id`),
    KEY `transfers_product_id_foreign` (`product_id`),
    CONSTRAINT `transfers_from_user_id_foreign` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `transfers_to_user_id_foreign` FOREIGN KEY (`to_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `transfers_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create returns table
CREATE TABLE IF NOT EXISTS `returns` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) unsigned NOT NULL,
    `product_id` bigint(20) unsigned NOT NULL,
    `quantity` int(11) NOT NULL,
    `price` decimal(10,2) NOT NULL,
    `reason` text NOT NULL,
    `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `returns_user_id_foreign` (`user_id`),
    KEY `returns_product_id_foreign` (`product_id`),
    CONSTRAINT `returns_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `returns_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample products
INSERT IGNORE INTO `products` (`name`, `quantity`, `created_at`, `updated_at`) VALUES
('Apple', 500, NOW(), NOW()),
('Banana', 500, NOW(), NOW()),
('Mango', 500, NOW(), NOW());

-- Insert migration records
INSERT IGNORE INTO `migrations` (`migration`, `batch`) VALUES
('2024_01_01_000005_create_po_recieved_table', 2),
('2024_01_01_000006_create_po_recieved_items_table', 2),
('2024_01_01_000008_create_products_table', 3),
('2024_01_01_000009_create_stock_movements_table', 3),
('2024_01_01_000010_create_supplies_table', 3),
('2024_01_01_000011_create_supply_items_table', 3),
('2024_01_01_000012_create_transfers_table', 3),
('2024_01_01_000013_create_returns_table', 3),
('2024_01_01_000014_add_status_to_po_recieved_table', 3);
