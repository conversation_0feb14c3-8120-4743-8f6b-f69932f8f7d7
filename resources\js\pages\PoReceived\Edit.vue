<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { Plus, Trash2, ArrowLeft } from 'lucide-vue-next';

interface PoReceivedItem {
    id?: number;
    product_name: string;
    quantity: number;
    unit: string;
    price: number;
    total?: number;
}

interface PoReceived {
    id: number;
    institution_name: string;
    address: string;
    email: string;
    phone: string;
    total_amount: number;
    items: PoReceivedItem[];
}

interface Props {
    poReceived: PoReceived;
}

const props = defineProps<Props>();

const form = useForm({
    institution_name: props.poReceived.institution_name,
    address: props.poReceived.address,
    email: props.poReceived.email,
    phone: props.poReceived.phone,
    items: props.poReceived.items.map(item => ({
        product_name: item.product_name,
        quantity: item.quantity,
        unit: item.unit,
        price: item.price
    }))
});

const addItem = () => {
    form.items.push({
        product_name: '',
        quantity: 1,
        unit: 'pcs',
        price: 0
    });
};

const removeItem = (index: number) => {
    if (form.items.length > 1) {
        form.items.splice(index, 1);
    }
};

const calculateItemTotal = (item: PoReceivedItem) => {
    return item.quantity * item.price;
};

const calculateGrandTotal = () => {
    return form.items.reduce((total, item) => total + calculateItemTotal(item), 0);
};

const submit = () => {
    form.put(route('po-received.update', props.poReceived.id));
};
</script>

<template>
    <Head :title="`Edit PO Received - ${poReceived.institution_name}`" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center space-x-4">
                <Button variant="outline" size="sm" as-child>
                    <Link :href="route('po-received.show', poReceived.id)">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back
                    </Link>
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Edit PO Received</h1>
                    <p class="text-muted-foreground">
                        Update purchase order details
                    </p>
                </div>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
                <!-- Institution Details -->
                <Card>
                    <CardHeader>
                        <CardTitle>Institution Details</CardTitle>
                        <CardDescription>
                            Update the details of the institution/hospital
                        </CardDescription>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="institution_name">Institution/Hospital Name</Label>
                                <Input
                                    id="institution_name"
                                    v-model="form.institution_name"
                                    type="text"
                                    required
                                />
                                <InputError :message="form.errors.institution_name" />
                            </div>
                            <div class="space-y-2">
                                <Label for="email">Email</Label>
                                <Input
                                    id="email"
                                    v-model="form.email"
                                    type="email"
                                    required
                                />
                                <InputError :message="form.errors.email" />
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="phone">Phone</Label>
                                <Input
                                    id="phone"
                                    v-model="form.phone"
                                    type="tel"
                                    required
                                />
                                <InputError :message="form.errors.phone" />
                            </div>
                        </div>
                        <div class="space-y-2">
                            <Label for="address">Address</Label>
                            <textarea
                                id="address"
                                v-model="form.address"
                                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                required
                            ></textarea>
                            <InputError :message="form.errors.address" />
                        </div>
                    </CardContent>
                </Card>

                <!-- Products/Items -->
                <Card>
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle>Products/Items</CardTitle>
                                <CardDescription>
                                    Update products and their details
                                </CardDescription>
                            </div>
                            <Button type="button" @click="addItem" variant="outline">
                                <Plus class="mr-2 h-4 w-4" />
                                Add Item
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div v-for="(item, index) in form.items" :key="index" class="border rounded-lg p-4 space-y-4">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium">Item {{ index + 1 }}</h4>
                                <Button
                                    v-if="form.items.length > 1"
                                    type="button"
                                    @click="removeItem(index)"
                                    variant="destructive"
                                    size="sm"
                                >
                                    <Trash2 class="h-4 w-4" />
                                </Button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div class="space-y-2">
                                    <Label :for="`product_name_${index}`">Product Name</Label>
                                    <Input
                                        :id="`product_name_${index}`"
                                        v-model="item.product_name"
                                        type="text"
                                        required
                                    />
                                    <InputError :message="form.errors[`items.${index}.product_name`]" />
                                </div>
                                <div class="space-y-2">
                                    <Label :for="`quantity_${index}`">Quantity</Label>
                                    <Input
                                        :id="`quantity_${index}`"
                                        v-model.number="item.quantity"
                                        type="number"
                                        min="1"
                                        required
                                    />
                                    <InputError :message="form.errors[`items.${index}.quantity`]" />
                                </div>
                                <div class="space-y-2">
                                    <Label :for="`unit_${index}`">Unit</Label>
                                    <Input
                                        :id="`unit_${index}`"
                                        v-model="item.unit"
                                        type="text"
                                        required
                                    />
                                    <InputError :message="form.errors[`items.${index}.unit`]" />
                                </div>
                                <div class="space-y-2">
                                    <Label :for="`price_${index}`">Price</Label>
                                    <Input
                                        :id="`price_${index}`"
                                        v-model.number="item.price"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        required
                                    />
                                    <InputError :message="form.errors[`items.${index}.price`]" />
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-muted-foreground">
                                    Total: ${{ calculateItemTotal(item).toFixed(2) }}
                                </p>
                            </div>
                        </div>
                        
                        <div class="border-t pt-4">
                            <div class="text-right">
                                <p class="text-lg font-semibold">
                                    Grand Total: ${{ calculateGrandTotal().toFixed(2) }}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <div class="flex items-center justify-end space-x-4">
                    <Button type="button" variant="outline" as-child>
                        <Link :href="route('po-received.show', poReceived.id)">Cancel</Link>
                    </Button>
                    <Button type="submit" :disabled="form.processing">
                        {{ form.processing ? 'Updating...' : 'Update PO Received' }}
                    </Button>
                </div>
            </form>
        </div>
    </AppLayout>
</template>
