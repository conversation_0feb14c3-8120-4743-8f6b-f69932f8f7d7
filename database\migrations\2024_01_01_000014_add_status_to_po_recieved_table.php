<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('po_recieved', function (Blueprint $table) {
            $table->enum('status', ['pending', 'partially_supplied', 'supplied'])->default('pending')->after('total_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('po_recieved', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
