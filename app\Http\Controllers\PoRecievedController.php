<?php

namespace App\Http\Controllers;

use App\Models\PoRecieved;
use App\Models\PoRecievedItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class PoRecievedController extends Controller
{
    /**
     * Display a listing of PO Recieved
     */
    public function index(): Response
    {
        $poRecieved = PoRecieved::with(['user:id,name', 'items'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('PoRecieved/Index', [
            'poRecieved' => $poRecieved,
        ]);
    }

    /**
     * Show the form for creating a new PO Recieved
     */
    public function create(): Response
    {
        return Inertia::render('PoRecieved/Create');
    }

    /**
     * Store a newly created PO Recieved
     */
    public function store(Request $request)
    {
        $request->validate([
            'po_number' => 'required|string|max:255|unique:po_recieved',
            'po_date' => 'required|date',
            'po_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'institution_name' => 'required|string|max:255',
            'address' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0',
        ]);

        DB::transaction(function () use ($request) {
            // Handle file upload
            $imagePath = null;
            if ($request->hasFile('po_image')) {
                $imagePath = $request->file('po_image')->store('po_images', 'public');
            }

            $poRecieved = PoRecieved::create([
                'user_id' => auth()->id(),
                'po_number' => $request->po_number,
                'po_date' => $request->po_date,
                'po_image' => $imagePath,
                'institution_name' => $request->institution_name,
                'address' => $request->address,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            foreach ($request->items as $item) {
                PoRecievedItem::create([
                    'po_recieved_id' => $poRecieved->id,
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                ]);
            }
        });

        return redirect()->route('po-recieved.index')
            ->with('success', 'PO Recieved created successfully.');
    }

    /**
     * Display the specified PO Recieved
     */
    public function show(PoRecieved $poRecieved): Response
    {
        $poRecieved->load(['user:id,name', 'items']);

        return Inertia::render('PoRecieved/Show', [
            'poRecieved' => $poRecieved,
        ]);
    }

    /**
     * Show the form for editing the specified PO Recieved
     */
    public function edit(PoRecieved $poRecieved): Response
    {
        $poRecieved->load('items');

        return Inertia::render('PoRecieved/Edit', [
            'poRecieved' => $poRecieved,
        ]);
    }

    /**
     * Update the specified PO Recieved
     */
    public function update(Request $request, PoRecieved $poRecieved)
    {
        $request->validate([
            'po_number' => 'required|string|max:255|unique:po_recieved,po_number,' . $poRecieved->id,
            'po_date' => 'required|date',
            'po_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'institution_name' => 'required|string|max:255',
            'address' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0',
        ]);

        DB::transaction(function () use ($request, $poRecieved) {
            // Handle file upload
            $updateData = [
                'po_number' => $request->po_number,
                'po_date' => $request->po_date,
                'institution_name' => $request->institution_name,
                'address' => $request->address,
                'email' => $request->email,
                'phone' => $request->phone,
            ];

            if ($request->hasFile('po_image')) {
                // Delete old image if exists
                if ($poRecieved->po_image) {
                    Storage::disk('public')->delete($poRecieved->po_image);
                }
                $updateData['po_image'] = $request->file('po_image')->store('po_images', 'public');
            }

            $poRecieved->update($updateData);

            // Delete existing items
            $poRecieved->items()->delete();

            // Create new items
            foreach ($request->items as $item) {
                PoRecievedItem::create([
                    'po_recieved_id' => $poRecieved->id,
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                ]);
            }
        });

        return redirect()->route('po-recieved.index')
            ->with('success', 'PO Recieved updated successfully.');
    }

    /**
     * Remove the specified PO Recieved
     */
    public function destroy(PoRecieved $poRecieved)
    {
        $poRecieved->delete();

        return redirect()->route('po-recieved.index')
            ->with('success', 'PO Recieved deleted successfully.');
    }
}
