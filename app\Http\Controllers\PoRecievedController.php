<?php

namespace App\Http\Controllers;

use App\Models\PoRecieved;
use App\Models\PoRecievedItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class PoRecievedController extends Controller
{
    /**
     * Display a listing of PO Recieved
     */
    public function index(): Response
    {
        $poRecieved = PoRecieved::with(['user:id,name', 'items'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('PoRecieved/Index', [
            'poRecieved' => $poRecieved,
        ]);
    }

    /**
     * Show the form for creating a new PO Recieved
     */
    public function create(): Response
    {
        return Inertia::render('PoRecieved/Create');
    }

    /**
     * Store a newly created PO Recieved
     */
    public function store(Request $request)
    {
        $request->validate([
            'institution_name' => 'required|string|max:255',
            'address' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit' => 'required|string|max:50',
            'items.*.price' => 'required|numeric|min:0',
        ]);

        DB::transaction(function () use ($request) {
            $poRecieved = PoRecieved::create([
                'user_id' => auth()->id(),
                'institution_name' => $request->institution_name,
                'address' => $request->address,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            foreach ($request->items as $item) {
                PoRecievedItem::create([
                    'po_recieved_id' => $poRecieved->id,
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'unit' => $item['unit'],
                    'price' => $item['price'],
                ]);
            }
        });

        return redirect()->route('po-recieved.index')
            ->with('success', 'PO Recieved created successfully.');
    }

    /**
     * Display the specified PO Recieved
     */
    public function show(PoRecieved $poRecieved): Response
    {
        $poRecieved->load(['user:id,name', 'items']);

        return Inertia::render('PoRecieved/Show', [
            'poRecieved' => $poRecieved,
        ]);
    }

    /**
     * Show the form for editing the specified PO Recieved
     */
    public function edit(PoRecieved $poRecieved): Response
    {
        $poRecieved->load('items');

        return Inertia::render('PoRecieved/Edit', [
            'poRecieved' => $poRecieved,
        ]);
    }

    /**
     * Update the specified PO Recieved
     */
    public function update(Request $request, PoRecieved $poRecieved)
    {
        $request->validate([
            'institution_name' => 'required|string|max:255',
            'address' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'items' => 'required|array|min:1',
            'items.*.product_name' => 'required|string|max:255',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit' => 'required|string|max:50',
            'items.*.price' => 'required|numeric|min:0',
        ]);

        DB::transaction(function () use ($request, $poRecieved) {
            $poRecieved->update([
                'institution_name' => $request->institution_name,
                'address' => $request->address,
                'email' => $request->email,
                'phone' => $request->phone,
            ]);

            // Delete existing items
            $poRecieved->items()->delete();

            // Create new items
            foreach ($request->items as $item) {
                PoRecievedItem::create([
                    'po_recieved_id' => $poRecieved->id,
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'unit' => $item['unit'],
                    'price' => $item['price'],
                ]);
            }
        });

        return redirect()->route('po-recieved.index')
            ->with('success', 'PO Recieved updated successfully.');
    }

    /**
     * Remove the specified PO Recieved
     */
    public function destroy(PoRecieved $poRecieved)
    {
        $poRecieved->delete();

        return redirect()->route('po-recieved.index')
            ->with('success', 'PO Recieved deleted successfully.');
    }
}
