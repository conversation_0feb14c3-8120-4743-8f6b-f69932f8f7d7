<?php

require_once 'vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

// Database configuration
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '127.0.0.1',
    'database' => 'order_system',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // Create po_recieved table
    Capsule::schema()->create('po_recieved', function ($table) {
        $table->id();
        $table->foreignId('user_id')->constrained()->onDelete('cascade');
        $table->string('institution_name');
        $table->text('address');
        $table->string('email');
        $table->string('phone');
        $table->decimal('total_amount', 10, 2)->default(0);
        $table->timestamps();
    });
    
    echo "✅ po_recieved table created successfully\n";
    
    // Create po_recieved_items table
    Capsule::schema()->create('po_recieved_items', function ($table) {
        $table->id();
        $table->foreignId('po_recieved_id')->constrained('po_recieved')->onDelete('cascade');
        $table->string('product_name');
        $table->integer('quantity');
        $table->string('unit')->default('pcs');
        $table->decimal('price', 10, 2);
        $table->decimal('total', 10, 2);
        $table->timestamps();
    });
    
    echo "✅ po_recieved_items table created successfully\n";
    
    // Update migrations table
    Capsule::table('migrations')->insert([
        ['migration' => '2024_01_01_000005_create_po_recieved_table', 'batch' => 2],
        ['migration' => '2024_01_01_000006_create_po_recieved_items_table', 'batch' => 2]
    ]);
    
    echo "✅ Migration records added successfully\n";
    echo "🎉 All tables created successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
