<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new columns to po_recieved table
        Schema::table('po_recieved', function (Blueprint $table) {
            $table->string('po_number')->after('user_id');
            $table->date('po_date')->after('po_number');
            $table->string('po_image')->nullable()->after('po_date');
        });

        // Remove unit column from po_recieved_items table
        Schema::table('po_recieved_items', function (Blueprint $table) {
            $table->dropColumn('unit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('po_recieved', function (Blueprint $table) {
            $table->dropColumn(['po_number', 'po_date', 'po_image']);
        });

        Schema::table('po_recieved_items', function (Blueprint $table) {
            $table->string('unit')->default('pcs')->after('quantity');
        });
    }
};
