<?php

namespace App\Http\Controllers;

use App\Models\DeliveryChallan;
use App\Models\Invoice;
use App\Models\Supply;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class DocumentController extends Controller
{
    /**
     * Generate DC and Invoice for a PO (finds latest supply)
     */
    public function generateDocuments(Request $request, $poId)
    {
        // Find the latest supply for this PO
        $supply = Supply::with(['poRecieved.items', 'items.poRecievedItem', 'user'])
            ->where('po_recieved_id', $poId)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$supply) {
            return response()->json([
                'success' => false,
                'message' => 'No supply found for this PO'
            ], 404);
        }

        // Check if documents already exist for this supply
        $existingDc = DeliveryChallan::where('supply_id', $supply->id)->first();
        $existingInvoice = Invoice::where('supply_id', $supply->id)->first();

        if ($existingDc && $existingInvoice) {
            return response()->json([
                'success' => true,
                'dc_id' => $existingDc->id,
                'invoice_id' => $existingInvoice->id,
                'message' => 'Documents already exist for this supply'
            ]);
        }

        // Calculate total amount
        $totalAmount = 0;
        foreach ($supply->items as $item) {
            $totalAmount += $item->quantity_supplied * $item->poRecievedItem->price;
        }

        // Create Delivery Challan if not exists
        if (!$existingDc) {
            $dc = DeliveryChallan::create([
                'supply_id' => $supply->id,
                'dc_number' => DeliveryChallan::generateDcNumber(),
                'dc_date' => now()->toDateString(),
                'total_amount' => $totalAmount,
                'notes' => 'Generated for PO #' . $supply->poRecieved->po_number,
            ]);
        } else {
            $dc = $existingDc;
        }

        // Create Invoice if not exists
        if (!$existingInvoice) {
            $subtotal = $totalAmount;
            $taxAmount = $subtotal * 0.18; // 18% GST
            $total = $subtotal + $taxAmount;

            $invoice = Invoice::create([
                'supply_id' => $supply->id,
                'invoice_number' => Invoice::generateInvoiceNumber(),
                'invoice_date' => now()->toDateString(),
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $total,
                'notes' => 'Generated for PO #' . $supply->poRecieved->po_number,
            ]);
        } else {
            $invoice = $existingInvoice;
        }

        return response()->json([
            'success' => true,
            'dc_id' => $dc->id,
            'invoice_id' => $invoice->id,
            'message' => 'DC and Invoice generated successfully'
        ]);
    }

    /**
     * View DC PDF
     */
    public function viewDc($id)
    {
        $dc = DeliveryChallan::with(['supply.poRecieved', 'supply.items.poRecievedItem', 'supply.user'])
            ->findOrFail($id);

        return Inertia::render('Documents/DeliveryChallan', [
            'dc' => $dc,
        ]);
    }

    /**
     * View Invoice PDF
     */
    public function viewInvoice($id)
    {
        $invoice = Invoice::with(['supply.poRecieved', 'supply.items.poRecievedItem', 'supply.user'])
            ->findOrFail($id);

        return Inertia::render('Documents/Invoice', [
            'invoice' => $invoice,
        ]);
    }

    /**
     * Generate DC HTML for printing
     */
    public function printDc($id)
    {
        $dc = DeliveryChallan::with(['supply.poRecieved', 'supply.items.poRecievedItem', 'supply.user'])
            ->findOrFail($id);

        return view('documents.dc', compact('dc'));
    }

    /**
     * Generate Invoice HTML for printing
     */
    public function printInvoice($id)
    {
        $invoice = Invoice::with(['supply.poRecieved', 'supply.items.poRecievedItem', 'supply.user'])
            ->findOrFail($id);

        return view('documents.invoice', compact('invoice'));
    }

    /**
     * List all documents
     */
    public function index()
    {
        $dcs = DeliveryChallan::with(['supply.poRecieved'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        $invoices = Invoice::with(['supply.poRecieved'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('Documents/Index', [
            'dcs' => $dcs,
            'invoices' => $invoices,
        ]);
    }

    /**
     * Upload DC proof image
     */
    public function uploadDcProof(Request $request, $id)
    {
        $dc = DeliveryChallan::findOrFail($id);

        $request->validate([
            'dc_proof_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Delete old image if exists
        if ($dc->dc_proof_image) {
            Storage::disk('public')->delete($dc->dc_proof_image);
        }

        // Store new image
        $imagePath = $request->file('dc_proof_image')->store('documents/dc', 'public');

        $dc->update(['dc_proof_image' => $imagePath]);

        return response()->json([
            'success' => true,
            'message' => 'DC proof uploaded successfully'
        ]);
    }

    /**
     * Upload Invoice proof image
     */
    public function uploadInvoiceProof(Request $request, $id)
    {
        $invoice = Invoice::findOrFail($id);

        $request->validate([
            'invoice_proof_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Delete old image if exists
        if ($invoice->invoice_proof_image) {
            Storage::disk('public')->delete($invoice->invoice_proof_image);
        }

        // Store new image
        $imagePath = $request->file('invoice_proof_image')->store('documents/invoice', 'public');

        $invoice->update(['invoice_proof_image' => $imagePath]);

        return response()->json([
            'success' => true,
            'message' => 'Invoice proof uploaded successfully'
        ]);
    }
}
