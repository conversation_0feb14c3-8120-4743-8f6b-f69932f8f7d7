<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    echo "🔧 Setting up Supply Test Data...\n";
    
    // Step 1: Clear all data
    echo "Step 1: Clearing all data...\n";
    $pdo->exec("DELETE FROM stock_movements");
    $pdo->exec("DELETE FROM supply_items");
    $pdo->exec("DELETE FROM supplies");
    $pdo->exec("DELETE FROM delivery_challans");
    $pdo->exec("DELETE FROM invoices");
    $pdo->exec("DELETE FROM transfers");
    $pdo->exec("DELETE FROM returns");
    $pdo->exec("DELETE FROM payments");
    $pdo->exec("DELETE FROM products");
    $pdo->exec("DELETE FROM po_recieved_items WHERE po_recieved_id > 0");
    $pdo->exec("DELETE FROM po_recieved WHERE id > 0");
    echo "✅ All data cleared\n";
    
    // Step 2: Get admin user
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "❌ Admin user not found. Please run verify_database.php first.\n";
        exit;
    }
    echo "✅ Admin user found\n";
    
    // Step 3: Create products with exact names
    echo "Step 3: Creating products...\n";
    $products = [
        ['name' => 'Mango', 'quantity' => 100],
        ['name' => 'Apple', 'quantity' => 150],
        ['name' => 'Banana', 'quantity' => 200],
        ['name' => 'Orange', 'quantity' => 75],
        ['name' => 'Grapes', 'quantity' => 120]
    ];
    
    $productIds = [];
    foreach ($products as $productData) {
        // Insert product
        $stmt = $pdo->prepare("INSERT INTO products (name, quantity, created_at, updated_at) VALUES (?, ?, NOW(), NOW())");
        $stmt->execute([$productData['name'], $productData['quantity']]);
        $productId = $pdo->lastInsertId();
        $productIds[$productData['name']] = $productId;
        
        // Create initial stock movement
        $stmt = $pdo->prepare("INSERT INTO stock_movements (product_id, user_id, type, quantity, reference_type, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->execute([
            $productId,
            $admin['id'],
            'in',
            $productData['quantity'],
            'initial',
            'Initial stock for ' . $productData['name']
        ]);
        
        echo "✅ Created {$productData['name']} with {$productData['quantity']} units (ID: $productId)\n";
    }
    
    // Step 4: Create test PO with EXACT matching product names
    echo "Step 4: Creating test PO with exact product names...\n";
    $stmt = $pdo->prepare("INSERT INTO po_recieved (po_number, po_date, institution_name, address, email, phone, total_amount, status, user_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
    $stmt->execute([
        'PO-SUPPLY-TEST-001',
        date('Y-m-d'),
        'Supply Test Institution',
        '123 Supply Test Street',
        '<EMAIL>',
        '+1234567890',
        0, // Will calculate below
        'pending',
        $admin['id']
    ]);
    $poId = $pdo->lastInsertId();
    
    // Add PO items with EXACT product names
    $poItems = [
        ['product_name' => 'Mango', 'quantity' => 10, 'price' => 8.00],
        ['product_name' => 'Apple', 'quantity' => 15, 'price' => 5.00],
        ['product_name' => 'Banana', 'quantity' => 20, 'price' => 3.00]
    ];
    
    $totalAmount = 0;
    foreach ($poItems as $item) {
        $itemTotal = $item['quantity'] * $item['price'];
        $totalAmount += $itemTotal;
        
        $stmt = $pdo->prepare("INSERT INTO po_recieved_items (po_recieved_id, product_name, quantity, price, total, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->execute([
            $poId,
            $item['product_name'],
            $item['quantity'],
            $item['price'],
            $itemTotal
        ]);
        
        echo "✅ Added PO item: {$item['product_name']} x {$item['quantity']} @ \${$item['price']} = \${$itemTotal}\n";
    }
    
    // Update PO total
    $stmt = $pdo->prepare("UPDATE po_recieved SET total_amount = ? WHERE id = ?");
    $stmt->execute([$totalAmount, $poId]);
    
    echo "✅ Test PO created (ID: $poId) with total: \${$totalAmount}\n";
    
    // Step 5: Verify data
    echo "\nStep 5: Verifying data...\n";
    
    // Check products
    $stmt = $pdo->query("SELECT * FROM products ORDER BY name");
    $dbProducts = $stmt->fetchAll();
    echo "📦 Products in database:\n";
    foreach ($dbProducts as $product) {
        echo "   - {$product['name']}: {$product['quantity']} units (ID: {$product['id']})\n";
    }
    
    // Check PO items
    $stmt = $pdo->prepare("SELECT * FROM po_recieved_items WHERE po_recieved_id = ?");
    $stmt->execute([$poId]);
    $dbPoItems = $stmt->fetchAll();
    echo "\n📋 PO items in database:\n";
    foreach ($dbPoItems as $item) {
        echo "   - {$item['product_name']}: {$item['quantity']} units @ \${$item['price']} (ID: {$item['id']})\n";
    }
    
    echo "\n🎉 Supply test setup complete!\n";
    echo "\n📋 Test Data Summary:\n";
    echo "✅ 5 Products created with stock\n";
    echo "✅ 1 Test PO with exact product name matches\n";
    echo "✅ All stock movements tracked\n";
    echo "✅ Product names match exactly between inventory and PO\n";
    
    echo "\n🧪 Test Instructions:\n";
    echo "1. Start application: php artisan serve\n";
    echo "2. Start frontend: npm run dev\n";
    echo "3. Login as admin (<EMAIL> / password)\n";
    echo "4. Go to 'Supply' page\n";
    echo "5. Click 'Supply' button for PO-SUPPLY-TEST-001\n";
    echo "6. Enter quantities to supply:\n";
    echo "   - Mango: 5 units (out of 10 requested)\n";
    echo "   - Apple: 10 units (out of 15 requested)\n";
    echo "   - Banana: 15 units (out of 20 requested)\n";
    echo "7. Click 'Process Supply'\n";
    echo "8. Verify stock levels decrease correctly\n";
    echo "9. Check 'Stock' page to see updated quantities\n";
    
    echo "\n📊 Expected Results:\n";
    echo "Before Supply:\n";
    echo "- Mango: 100 units\n";
    echo "- Apple: 150 units\n";
    echo "- Banana: 200 units\n";
    echo "\nAfter Supply (if you supply 5 Mango, 10 Apple, 15 Banana):\n";
    echo "- Mango: 95 units (100 - 5)\n";
    echo "- Apple: 140 units (150 - 10)\n";
    echo "- Banana: 185 units (200 - 15)\n";
    
    echo "\n⚠️  Important Notes:\n";
    echo "- Product names in PO exactly match product names in inventory\n";
    echo "- Stock will decrease when supply is processed\n";
    echo "- Stock movements will be recorded\n";
    echo "- PO status will update to 'partially_supplied' or 'supplied'\n";
    echo "- You can then generate DC & Invoice documents\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
