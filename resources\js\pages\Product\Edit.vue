<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ArrowLeft, Package } from 'lucide-vue-next';

interface Product {
    id: number;
    name: string;
    quantity: number;
    created_at: string;
    updated_at: string;
}

interface Props {
    product: Product;
}

const props = defineProps<Props>();

const form = useForm({
    name: props.product.name,
    quantity: props.product.quantity,
});

const submit = () => {
    form.put(route('product.update', props.product.id));
};

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};
</script>

<template>
    <Head :title="`Edit Product - ${product.name}`" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center space-x-4">
                <Button variant="outline" size="sm" as-child>
                    <Link :href="route('product.index')">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back to Stock
                    </Link>
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Edit Product</h1>
                    <p class="text-muted-foreground">
                        Update product information
                    </p>
                </div>
            </div>

            <Card class="max-w-2xl">
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Package class="h-5 w-5" />
                        Product Information
                    </CardTitle>
                    <CardDescription>
                        Update the product name and current stock quantity
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="submit" class="space-y-4">
                        <div class="space-y-2">
                            <Label for="name">Product Name</Label>
                            <Input
                                id="name"
                                v-model="form.name"
                                type="text"
                                placeholder="Enter product name"
                                required
                            />
                            <InputError :message="form.errors.name" />
                        </div>

                        <div class="space-y-2">
                            <Label for="quantity">Current Stock Quantity</Label>
                            <Input
                                id="quantity"
                                v-model.number="form.quantity"
                                type="number"
                                min="0"
                                placeholder="0"
                                required
                            />
                            <p class="text-sm text-muted-foreground">
                                Current quantity in stock. Use stock management buttons for adding/removing stock with tracking.
                            </p>
                            <InputError :message="form.errors.quantity" />
                        </div>

                        <div class="bg-muted p-4 rounded-lg">
                            <h4 class="font-medium mb-2">Product Details</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p><strong>Created:</strong> {{ formatDate(product.created_at) }}</p>
                                    <p><strong>Last Updated:</strong> {{ formatDate(product.updated_at) }}</p>
                                </div>
                                <div>
                                    <p><strong>Product ID:</strong> {{ product.id }}</p>
                                    <p><strong>Current Stock:</strong> {{ product.quantity }} units</p>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-end space-x-4 pt-4">
                            <Button type="button" variant="outline" as-child>
                                <Link :href="route('product.index')">Cancel</Link>
                            </Button>
                            <Button type="submit" :disabled="form.processing">
                                {{ form.processing ? 'Updating...' : 'Update Product' }}
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>

            <!-- Quick Actions -->
            <Card class="max-w-2xl">
                <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                    <CardDescription>
                        Additional actions for this product
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="flex items-center space-x-4">
                        <Button variant="outline" as-child>
                            <Link :href="route('product.show', product.id)">
                                View Details & History
                            </Link>
                        </Button>
                        <Button variant="outline" as-child>
                            <Link :href="route('product.index')">
                                Back to Stock Management
                            </Link>
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
