<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    echo "🧹 Creating Blank Slate - Clearing ALL Data...\n\n";
    
    // Step 1: Clear all transactional data
    echo "Step 1: Clearing all transactional data...\n";
    $pdo->exec("DELETE FROM stock_movements");
    $pdo->exec("DELETE FROM supply_items");
    $pdo->exec("DELETE FROM supplies");
    $pdo->exec("DELETE FROM delivery_challans");
    $pdo->exec("DELETE FROM invoices");
    $pdo->exec("DELETE FROM transfers");
    $pdo->exec("DELETE FROM returns");
    $pdo->exec("DELETE FROM payments");
    echo "✅ Transactional data cleared\n";
    
    // Step 2: Clear all master data
    echo "Step 2: Clearing all master data...\n";
    $pdo->exec("DELETE FROM products");
    $pdo->exec("DELETE FROM po_recieved_items WHERE po_recieved_id > 0");
    $pdo->exec("DELETE FROM po_recieved WHERE id > 0");
    echo "✅ Master data cleared\n";
    
    // Step 3: Reset auto-increment IDs
    echo "Step 3: Resetting auto-increment IDs...\n";
    $tables = [
        'products', 'po_recieved', 'po_recieved_items', 'stock_movements', 
        'supplies', 'supply_items', 'delivery_challans', 'invoices', 
        'transfers', 'returns', 'payments'
    ];
    
    foreach ($tables as $table) {
        $pdo->exec("ALTER TABLE `$table` AUTO_INCREMENT = 1");
        echo "✅ Reset $table auto-increment\n";
    }
    
    // Step 4: Verify users still exist
    echo "Step 4: Verifying users...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    
    if ($userCount == 0) {
        echo "❌ No users found! Please run verify_database.php to create users.\n";
    } else {
        echo "✅ Found $userCount users (preserved)\n";
        
        // Show user list
        $stmt = $pdo->query("SELECT id, name, email, role FROM users ORDER BY role DESC, name");
        $users = $stmt->fetchAll();
        echo "   Users available:\n";
        foreach ($users as $user) {
            echo "   - {$user['name']} ({$user['email']}) - {$user['role']}\n";
        }
    }
    
    echo "\n🎉 Blank Slate Created Successfully!\n";
    echo "\n📋 What was cleared:\n";
    echo "✅ ALL products deleted\n";
    echo "✅ ALL purchase orders deleted\n";
    echo "✅ ALL stock movements deleted\n";
    echo "✅ ALL supply records deleted\n";
    echo "✅ ALL documents deleted\n";
    echo "✅ ALL transfers deleted\n";
    echo "✅ ALL returns deleted\n";
    echo "✅ ALL payments deleted\n";
    echo "✅ Auto-increment IDs reset to 1\n";
    echo "✅ Users preserved for login\n";
    
    echo "\n🚀 Next Steps - Complete CRUD Workflow:\n";
    echo "1. Start application: php artisan serve\n";
    echo "2. Start frontend: npm run dev\n";
    echo "3. Login as admin or user\n";
    echo "4. Go to 'Stock' page (Products)\n";
    echo "5. Click 'Add Product' to create your first product\n";
    echo "6. Use CRUD operations to manage products\n";
    echo "7. Create POs and test supply process\n";
    
    echo "\n📝 CRUD Operations Available:\n";
    echo "\n1. Product Management (Stock Page):\n";
    echo "   ✅ CREATE: Click 'Add Product' button\n";
    echo "   ✅ READ: View all products with stock levels\n";
    echo "   ✅ UPDATE: Click 'Edit' button (pencil icon)\n";
    echo "   ✅ DELETE: Click 'Delete' button (trash icon)\n";
    echo "   ✅ ADD STOCK: Click 'Add Stock' button\n";
    echo "   ✅ REMOVE STOCK: Click 'Remove Stock' button\n";
    echo "   ✅ VIEW DETAILS: Click 'View' button (eye icon)\n";
    
    echo "\n2. Stock Management Features:\n";
    echo "   ✅ Real-time stock tracking\n";
    echo "   ✅ Stock movement history\n";
    echo "   ✅ Add/Remove stock with notes\n";
    echo "   ✅ Automatic stock updates during supply\n";
    echo "   ✅ Stock validation (can't remove more than available)\n";
    echo "   ✅ Complete audit trail\n";
    
    echo "\n3. Supply Integration:\n";
    echo "   ✅ Products automatically linked to supply controller\n";
    echo "   ✅ Exact name matching for PO items\n";
    echo "   ✅ Stock decreases when supplies are processed\n";
    echo "   ✅ Stock movements recorded for all transactions\n";
    echo "   ✅ PO status updates based on supply completion\n";
    
    echo "\n📋 How to Test Complete Workflow:\n";
    echo "\n1. Create Products:\n";
    echo "   - Go to Stock page\n";
    echo "   - Click 'Add Product'\n";
    echo "   - Enter name (e.g., 'Apple', 'Mango', 'Banana')\n";
    echo "   - Set initial quantity (e.g., 100)\n";
    echo "   - Save product\n";
    
    echo "\n2. Manage Stock:\n";
    echo "   - Use 'Add Stock' to increase inventory\n";
    echo "   - Use 'Remove Stock' to decrease inventory\n";
    echo "   - View stock movement history\n";
    echo "   - Edit product details\n";
    
    echo "\n3. Create PO with Matching Products:\n";
    echo "   - Go to PO Recieved page\n";
    echo "   - Create new PO\n";
    echo "   - Add items with EXACT product names from your inventory\n";
    echo "   - Save PO\n";
    
    echo "\n4. Process Supply:\n";
    echo "   - Go to Supply page\n";
    echo "   - Click 'Supply' for your PO\n";
    echo "   - Enter quantities to supply\n";
    echo "   - Process supply\n";
    echo "   - Watch stock levels decrease automatically\n";
    
    echo "\n5. Generate Documents:\n";
    echo "   - Generate DC & Invoice after supply\n";
    echo "   - View professional PDF documents\n";
    echo "   - Upload proof images\n";
    
    echo "\n👥 Login Credentials:\n";
    echo "Admin: <EMAIL> / password\n";
    echo "Users: <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    
    echo "\n⚠️  Important Notes:\n";
    echo "- Start with creating products manually\n";
    echo "- Use exact product names for PO matching\n";
    echo "- Stock levels update automatically during supply\n";
    echo "- All operations are tracked with audit trail\n";
    echo "- Delete protection prevents removing products used in supplies\n";
    
    echo "\n🎯 You now have a completely blank system ready for manual product creation and testing!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
