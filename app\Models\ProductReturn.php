<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductReturn extends Model
{
    use HasFactory;

    protected $table = 'returns';

    protected $fillable = [
        'user_id',
        'product_id',
        'quantity',
        'price',
        'reason',
        'status',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price' => 'decimal:2',
    ];

    /**
     * Get the user for this return
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product for this return
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Approve the return
     */
    public function approve(): void
    {
        if ($this->status === 'pending') {
            // Create stock movement for return
            StockMovement::create([
                'product_id' => $this->product_id,
                'user_id' => $this->user_id,
                'type' => 'in',
                'quantity' => $this->quantity,
                'reference_type' => 'return',
                'reference_id' => $this->id,
                'notes' => "Return: {$this->reason}",
            ]);

            $this->status = 'approved';
            $this->save();
        }
    }

    /**
     * Reject the return
     */
    public function reject(): void
    {
        $this->status = 'rejected';
        $this->save();
    }
}
