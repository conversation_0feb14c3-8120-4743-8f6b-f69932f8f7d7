<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    echo "🔍 Debugging Supply Issue...\n\n";
    
    // Step 1: Check products
    echo "Step 1: Checking products in database...\n";
    $stmt = $pdo->query("SELECT id, name, quantity FROM products ORDER BY name");
    $products = $stmt->fetchAll();
    
    if (empty($products)) {
        echo "❌ No products found in database!\n";
        echo "   Run: php setup_supply_test.php to create test data\n\n";
    } else {
        echo "✅ Found " . count($products) . " products:\n";
        foreach ($products as $product) {
            echo "   - ID: {$product['id']}, Name: '{$product['name']}', Quantity: {$product['quantity']}\n";
        }
        echo "\n";
    }
    
    // Step 2: Check POs
    echo "Step 2: Checking POs in database...\n";
    $stmt = $pdo->query("SELECT id, po_number, institution_name, status FROM po_recieved ORDER BY created_at DESC LIMIT 5");
    $pos = $stmt->fetchAll();
    
    if (empty($pos)) {
        echo "❌ No POs found in database!\n";
        echo "   Run: php setup_supply_test.php to create test data\n\n";
    } else {
        echo "✅ Found " . count($pos) . " recent POs:\n";
        foreach ($pos as $po) {
            echo "   - ID: {$po['id']}, Number: {$po['po_number']}, Institution: {$po['institution_name']}, Status: {$po['status']}\n";
        }
        echo "\n";
    }
    
    // Step 3: Check PO items for the latest PO
    if (!empty($pos)) {
        $latestPo = $pos[0];
        echo "Step 3: Checking PO items for latest PO (ID: {$latestPo['id']})...\n";
        $stmt = $pdo->prepare("SELECT id, product_name, quantity, price FROM po_recieved_items WHERE po_recieved_id = ?");
        $stmt->execute([$latestPo['id']]);
        $poItems = $stmt->fetchAll();
        
        if (empty($poItems)) {
            echo "❌ No PO items found for this PO!\n\n";
        } else {
            echo "✅ Found " . count($poItems) . " PO items:\n";
            foreach ($poItems as $item) {
                echo "   - ID: {$item['id']}, Product: '{$item['product_name']}', Quantity: {$item['quantity']}, Price: \${$item['price']}\n";
                
                // Check if matching product exists
                $matchingProduct = null;
                foreach ($products as $product) {
                    if ($product['name'] === $item['product_name']) {
                        $matchingProduct = $product;
                        break;
                    }
                }
                
                if ($matchingProduct) {
                    echo "     ✅ Matching product found: '{$matchingProduct['name']}' with {$matchingProduct['quantity']} units\n";
                    if ($matchingProduct['quantity'] >= $item['quantity']) {
                        echo "     ✅ Sufficient stock available\n";
                    } else {
                        echo "     ❌ Insufficient stock! Need {$item['quantity']}, have {$matchingProduct['quantity']}\n";
                    }
                } else {
                    echo "     ❌ No matching product found!\n";
                    echo "     Available products: " . implode(', ', array_column($products, 'name')) . "\n";
                }
                echo "\n";
            }
        }
    }
    
    // Step 4: Check recent supplies
    echo "Step 4: Checking recent supply attempts...\n";
    $stmt = $pdo->query("SELECT id, po_recieved_id, user_id, status, created_at FROM supplies ORDER BY created_at DESC LIMIT 5");
    $supplies = $stmt->fetchAll();
    
    if (empty($supplies)) {
        echo "❌ No supply records found\n\n";
    } else {
        echo "✅ Found " . count($supplies) . " recent supply records:\n";
        foreach ($supplies as $supply) {
            echo "   - ID: {$supply['id']}, PO: {$supply['po_recieved_id']}, Status: {$supply['status']}, Date: {$supply['created_at']}\n";
        }
        echo "\n";
    }
    
    // Step 5: Check stock movements
    echo "Step 5: Checking recent stock movements...\n";
    $stmt = $pdo->query("SELECT sm.*, p.name as product_name FROM stock_movements sm JOIN products p ON sm.product_id = p.id ORDER BY sm.created_at DESC LIMIT 10");
    $movements = $stmt->fetchAll();
    
    if (empty($movements)) {
        echo "❌ No stock movements found\n\n";
    } else {
        echo "✅ Found " . count($movements) . " recent stock movements:\n";
        foreach ($movements as $movement) {
            echo "   - Product: {$movement['product_name']}, Type: {$movement['type']}, Quantity: {$movement['quantity']}, Reference: {$movement['reference_type']}, Date: {$movement['created_at']}\n";
        }
        echo "\n";
    }
    
    echo "🔧 Troubleshooting Guide:\n\n";
    
    if (empty($products)) {
        echo "❌ ISSUE: No products in database\n";
        echo "   SOLUTION: Run 'php setup_supply_test.php' to create test products\n\n";
    }
    
    if (empty($pos)) {
        echo "❌ ISSUE: No POs in database\n";
        echo "   SOLUTION: Run 'php setup_supply_test.php' to create test PO\n\n";
    }
    
    if (!empty($products) && !empty($pos) && !empty($poItems)) {
        echo "✅ Basic data exists. Checking for common issues:\n\n";
        
        $hasMatches = false;
        foreach ($poItems as $item) {
            foreach ($products as $product) {
                if ($product['name'] === $item['product_name']) {
                    $hasMatches = true;
                    if ($product['quantity'] < $item['quantity']) {
                        echo "⚠️  WARNING: Product '{$product['name']}' has insufficient stock\n";
                        echo "   Available: {$product['quantity']}, Requested: {$item['quantity']}\n";
                        echo "   SOLUTION: Add more stock or reduce supply quantity\n\n";
                    }
                }
            }
        }
        
        if (!$hasMatches) {
            echo "❌ ISSUE: No product name matches between PO items and inventory\n";
            echo "   PO Items: " . implode(', ', array_column($poItems, 'product_name')) . "\n";
            echo "   Products: " . implode(', ', array_column($products, 'name')) . "\n";
            echo "   SOLUTION: Ensure product names match exactly (case-sensitive)\n\n";
        }
    }
    
    echo "📋 Next Steps:\n";
    echo "1. If no data exists: Run 'php setup_supply_test.php'\n";
    echo "2. If data exists but supply fails:\n";
    echo "   - Check browser console for JavaScript errors\n";
    echo "   - Check Laravel logs: storage/logs/laravel.log\n";
    echo "   - Verify product names match exactly\n";
    echo "   - Ensure sufficient stock is available\n";
    echo "3. Test the supply process step by step\n";
    echo "4. Check the debug API: /api/debug/products\n\n";
    
    echo "🚀 Test Commands:\n";
    echo "1. Setup test data: php setup_supply_test.php\n";
    echo "2. Start server: php artisan serve\n";
    echo "3. Start frontend: npm run dev\n";
    echo "4. Check products API: curl http://localhost:8000/api/debug/products\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
