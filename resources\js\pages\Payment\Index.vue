<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { Plus, CreditCard, Check, X, Eye } from 'lucide-vue-next';

interface Payment {
    id: number;
    cheque_number: string;
    cheque_image: string | null;
    amount: number;
    payment_date: string;
    status: 'pending' | 'approved' | 'rejected';
    notes: string | null;
    created_at: string;
    poRecieved: {
        id: number;
        po_number: string;
        institution_name: string;
    };
    user: {
        id: number;
        name: string;
    };
}

interface Props {
    payments: {
        data: Payment[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'text-yellow-600 bg-yellow-100';
        case 'approved':
            return 'text-green-600 bg-green-100';
        case 'rejected':
            return 'text-red-600 bg-red-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
};

const approvePayment = (id: number) => {
    if (confirm('Are you sure you want to approve this payment?')) {
        router.post(route('payment.approve', id));
    }
};

const rejectPayment = (id: number) => {
    if (confirm('Are you sure you want to reject this payment?')) {
        router.post(route('payment.reject', id));
    }
};
</script>

<template>
    <Head title="Payments" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Payments</h1>
                    <p class="text-muted-foreground">
                        Manage payment records and cheque details
                    </p>
                </div>
                <Button as-child>
                    <Link :href="route('payment.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        Record Payment
                    </Link>
                </Button>
            </div>

            <div class="grid gap-4">
                <Card v-for="payment in payments.data" :key="payment.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    <CreditCard class="h-5 w-5" />
                                    Cheque #{{ payment.cheque_number }}
                                    <span :class="`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(payment.status)}`">
                                        {{ payment.status.toUpperCase() }}
                                    </span>
                                </CardTitle>
                                <CardDescription>
                                    PO: {{ payment.poRecieved.po_number }} • {{ payment.poRecieved.institution_name }}
                                </CardDescription>
                                <CardDescription>
                                    Recorded by {{ payment.user.name }} on {{ formatDate(payment.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="text-right mr-4">
                                    <p class="text-2xl font-bold">{{ formatCurrency(payment.amount) }}</p>
                                    <p class="text-sm text-muted-foreground">{{ formatDate(payment.payment_date) }}</p>
                                </div>
                                
                                <!-- Cheque Image -->
                                <div v-if="payment.cheque_image" class="mr-2">
                                    <img 
                                        :src="`/storage/${payment.cheque_image}`" 
                                        alt="Cheque Image" 
                                        class="w-16 h-16 object-cover rounded border cursor-pointer"
                                        @click="window.open(`/storage/${payment.cheque_image}`, '_blank')"
                                    />
                                </div>
                                
                                <!-- Admin Actions -->
                                <div v-if="payment.status === 'pending'" class="flex space-x-2">
                                    <Button size="sm" @click="approvePayment(payment.id)">
                                        <Check class="h-4 w-4" />
                                    </Button>
                                    <Button size="sm" variant="destructive" @click="rejectPayment(payment.id)">
                                        <X class="h-4 w-4" />
                                    </Button>
                                </div>
                                
                                <Button size="sm" variant="outline" as-child>
                                    <Link :href="route('payment.show', payment.id)">
                                        <Eye class="h-4 w-4" />
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent v-if="payment.notes">
                        <p class="text-sm text-muted-foreground">{{ payment.notes }}</p>
                    </CardContent>
                </Card>

                <div v-if="payments.data.length === 0" class="text-center py-12">
                    <CreditCard class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No payments</h3>
                    <p class="mt-1 text-sm text-muted-foreground">Get started by recording your first payment.</p>
                    <div class="mt-6">
                        <Button as-child>
                            <Link :href="route('payment.create')">
                                <Plus class="mr-2 h-4 w-4" />
                                Record Payment
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
