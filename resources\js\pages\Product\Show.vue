<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { ArrowLeft, Package, TrendingUp, TrendingDown, Edit } from 'lucide-vue-next';

interface StockMovement {
    id: number;
    type: 'in' | 'out';
    quantity: number;
    reference_type: string;
    reference_id: number | null;
    notes: string;
    created_at: string;
    user: {
        id: number;
        name: string;
    };
}

interface Product {
    id: number;
    name: string;
    quantity: number;
    created_at: string;
    updated_at: string;
    stockMovements: StockMovement[];
}

interface Props {
    product: Product;
}

defineProps<Props>();

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const formatDateTime = (date: string) => {
    return new Date(date).toLocaleString();
};

const getTotalStockIn = (movements: StockMovement[]) => {
    return movements
        .filter(movement => movement.type === 'in')
        .reduce((total, movement) => total + movement.quantity, 0);
};

const getTotalStockOut = (movements: StockMovement[]) => {
    return movements
        .filter(movement => movement.type === 'out')
        .reduce((total, movement) => total + movement.quantity, 0);
};

const getMovementIcon = (type: string) => {
    return type === 'in' ? TrendingUp : TrendingDown;
};

const getMovementColor = (type: string) => {
    return type === 'in' ? 'text-green-600' : 'text-red-600';
};
</script>

<template>
    <Head :title="`Product - ${product.name}`" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center space-x-4">
                <Button variant="outline" size="sm" as-child>
                    <Link :href="route('product.index')">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back
                    </Link>
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">{{ product.name }}</h1>
                    <p class="text-muted-foreground">
                        Product details and stock movement history
                    </p>
                </div>
                <Button as-child>
                    <Link :href="route('product.edit', product.id)">
                        <Edit class="mr-2 h-4 w-4" />
                        Edit Product
                    </Link>
                </Button>
            </div>

            <!-- Product Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Current Stock</CardTitle>
                        <Package class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{{ product.quantity }}</div>
                        <p class="text-xs text-muted-foreground">units available</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Total Stock In</CardTitle>
                        <TrendingUp class="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold text-green-600">{{ getTotalStockIn(product.stockMovements) }}</div>
                        <p class="text-xs text-muted-foreground">units received</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Total Stock Out</CardTitle>
                        <TrendingDown class="h-4 w-4 text-red-600" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold text-red-600">{{ getTotalStockOut(product.stockMovements) }}</div>
                        <p class="text-xs text-muted-foreground">units dispatched</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Created</CardTitle>
                        <Package class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-lg font-bold">{{ formatDate(product.created_at) }}</div>
                        <p class="text-xs text-muted-foreground">product added</p>
                    </CardContent>
                </Card>
            </div>

            <!-- Stock Movement History -->
            <Card>
                <CardHeader>
                    <CardTitle>Stock Movement History</CardTitle>
                    <CardDescription>
                        Complete history of all stock movements for this product
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div v-if="product.stockMovements.length > 0" class="space-y-4">
                        <div 
                            v-for="movement in product.stockMovements" 
                            :key="movement.id"
                            class="flex items-center justify-between p-4 border rounded-lg"
                        >
                            <div class="flex items-center space-x-4">
                                <div :class="`flex items-center justify-center w-10 h-10 rounded-full ${movement.type === 'in' ? 'bg-green-100' : 'bg-red-100'}`">
                                    <component :is="getMovementIcon(movement.type)" :class="`h-5 w-5 ${getMovementColor(movement.type)}`" />
                                </div>
                                <div>
                                    <p class="font-medium">{{ movement.reference_type }}</p>
                                    <p class="text-sm text-muted-foreground">
                                        by {{ movement.user.name }} on {{ formatDateTime(movement.created_at) }}
                                    </p>
                                    <p v-if="movement.notes" class="text-sm text-muted-foreground mt-1">
                                        {{ movement.notes }}
                                    </p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p :class="`text-lg font-bold ${getMovementColor(movement.type)}`">
                                    {{ movement.type === 'in' ? '+' : '-' }}{{ movement.quantity }}
                                </p>
                                <p class="text-sm text-muted-foreground">units</p>
                            </div>
                        </div>
                    </div>
                    <div v-else class="text-center py-8">
                        <Package class="mx-auto h-12 w-12 text-muted-foreground" />
                        <h3 class="mt-2 text-sm font-semibold text-gray-900">No stock movements</h3>
                        <p class="mt-1 text-sm text-muted-foreground">No stock movements recorded for this product yet.</p>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
