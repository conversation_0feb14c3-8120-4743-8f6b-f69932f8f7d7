<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PoRecievedItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'po_recieved_id',
        'product_name',
        'quantity',
        'price',
        'total',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price' => 'decimal:2',
        'total' => 'decimal:2',
    ];

    /**
     * Get the PO this item belongs to
     */
    public function poRecieved(): BelongsTo
    {
        return $this->belongsTo(PoRecieved::class);
    }

    /**
     * Calculate total when saving
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            $item->total = $item->quantity * $item->price;
        });

        static::saved(function ($item) {
            $item->poRecieved->updateTotalAmount();
        });

        static::deleted(function ($item) {
            $item->poRecieved->updateTotalAmount();
        });
    }
}
