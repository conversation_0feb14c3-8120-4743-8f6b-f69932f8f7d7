<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PoRecieved extends Model
{
    use HasFactory;

    protected $table = 'po_recieved';

    protected $fillable = [
        'user_id',
        'po_number',
        'po_date',
        'po_image',
        'institution_name',
        'address',
        'email',
        'phone',
        'total_amount',
    ];

    protected $casts = [
        'po_date' => 'date',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the user that created this PO
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the items for this PO
     */
    public function items(): HasMany
    {
        return $this->hasMany(PoRecievedItem::class);
    }

    /**
     * Calculate and update total amount
     */
    public function updateTotalAmount(): void
    {
        $this->total_amount = $this->items()->sum('total');
        $this->save();
    }
}
