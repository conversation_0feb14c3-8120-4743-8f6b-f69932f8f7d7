<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('po_received_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('po_received_id')->constrained('po_received')->onDelete('cascade');
            $table->string('product_name');
            $table->integer('quantity');
            $table->string('unit')->default('pcs');
            $table->decimal('price', 10, 2);
            $table->decimal('total', 10, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('po_received_items');
    }
};
