<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'quantity',
    ];

    protected $casts = [
        'quantity' => 'integer',
    ];

    /**
     * Get the stock movements for this product
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class)->orderBy('created_at', 'desc');
    }

    /**
     * Update product quantity based on stock movements
     */
    public function updateQuantityFromMovements(): void
    {
        $totalIn = $this->stockMovements()->where('type', 'in')->sum('quantity');
        $totalOut = $this->stockMovements()->where('type', 'out')->sum('quantity');

        $this->update(['quantity' => $totalIn - $totalOut]);
    }

    /**
     * Get current stock level
     */
    public function getCurrentStock(): int
    {
        return $this->quantity;
    }

    /**
     * Check if product has sufficient stock
     */
    public function hasSufficientStock(int $requiredQuantity): bool
    {
        return $this->quantity >= $requiredQuantity;
    }

    /**
     * Get the transfers for this product
     */
    public function transfers(): HasMany
    {
        return $this->hasMany(Transfer::class);
    }

    /**
     * Get the returns for this product
     */
    public function returns(): HasMany
    {
        return $this->hasMany(ProductReturn::class);
    }

    /**
     * Update product quantity based on stock movements
     */
    public function updateQuantity(): void
    {
        $stockIn = $this->stockMovements()->where('type', 'in')->sum('quantity');
        $stockOut = $this->stockMovements()->where('type', 'out')->sum('quantity');

        $this->quantity = $stockIn - $stockOut;
        $this->save();
    }
}
