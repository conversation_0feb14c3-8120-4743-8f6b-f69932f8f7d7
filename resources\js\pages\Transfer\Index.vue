<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { Plus, ArrowLeftRight } from 'lucide-vue-next';

interface Transfer {
    id: number;
    quantity: number;
    status: 'pending' | 'completed';
    notes: string;
    created_at: string;
    fromUser: {
        id: number;
        name: string;
    };
    toUser: {
        id: number;
        name: string;
    };
    product: {
        id: number;
        name: string;
    };
}

interface Props {
    transfers: {
        data: Transfer[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'text-yellow-600 bg-yellow-100';
        case 'completed':
            return 'text-green-600 bg-green-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
};
</script>

<template>
    <Head title="Transfers" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Transfers</h1>
                    <p class="text-muted-foreground">
                        Manage product transfers between users
                    </p>
                </div>
                <Button as-child>
                    <Link :href="route('transfer.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        New Transfer
                    </Link>
                </Button>
            </div>

            <div class="grid gap-4">
                <Card v-for="transfer in transfers.data" :key="transfer.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    <ArrowLeftRight class="h-5 w-5" />
                                    {{ transfer.product.name }}
                                    <span :class="`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(transfer.status)}`">
                                        {{ transfer.status.toUpperCase() }}
                                    </span>
                                </CardTitle>
                                <CardDescription>
                                    {{ transfer.fromUser.name }} → {{ transfer.toUser.name }} • {{ formatDate(transfer.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold">{{ transfer.quantity }}</p>
                                <p class="text-sm text-muted-foreground">units</p>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent v-if="transfer.notes">
                        <p class="text-sm text-muted-foreground">{{ transfer.notes }}</p>
                    </CardContent>
                </Card>

                <div v-if="transfers.data.length === 0" class="text-center py-12">
                    <ArrowLeftRight class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No transfers</h3>
                    <p class="mt-1 text-sm text-muted-foreground">Get started by creating a new transfer.</p>
                    <div class="mt-6">
                        <Button as-child>
                            <Link :href="route('transfer.create')">
                                <Plus class="mr-2 h-4 w-4" />
                                New Transfer
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
