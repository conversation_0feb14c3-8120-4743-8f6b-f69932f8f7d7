<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import { Plus, Edit, Trash2, Users } from 'lucide-vue-next';

interface User {
    id: number;
    name: string;
    email: string;
    role: 'admin' | 'user';
    created_at: string;
}

interface Props {
    users: {
        data: User[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const getRoleBadgeVariant = (role: string) => {
    return role === 'admin' ? 'default' : 'secondary';
};
</script>

<template>
    <Head title="Manage Users" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Manage Users</h1>
                    <p class="text-muted-foreground">
                        Add and manage system users
                    </p>
                </div>
                <Button as-child>
                    <Link :href="route('admin.users.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        Add User
                    </Link>
                </Button>
            </div>

            <div class="grid gap-4">
                <Card v-for="user in users.data" :key="user.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    {{ user.name }}
                                    <Badge :variant="getRoleBadgeVariant(user.role)">
                                        {{ user.role }}
                                    </Badge>
                                </CardTitle>
                                <CardDescription>
                                    {{ user.email }} • Joined {{ formatDate(user.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button variant="outline" size="sm" as-child>
                                    <Link :href="route('admin.users.edit', user.id)">
                                        <Edit class="h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button variant="destructive" size="sm">
                                    <Trash2 class="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                </Card>

                <div v-if="users.data.length === 0" class="text-center py-12">
                    <Users class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No users</h3>
                    <p class="mt-1 text-sm text-muted-foreground">Get started by creating a new user.</p>
                    <div class="mt-6">
                        <Button as-child>
                            <Link :href="route('admin.users.create')">
                                <Plus class="mr-2 h-4 w-4" />
                                Add User
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
