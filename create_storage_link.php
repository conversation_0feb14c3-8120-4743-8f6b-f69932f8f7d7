<?php

// Create storage link manually
$target = __DIR__ . '/storage/app/public';
$link = __DIR__ . '/public/storage';

// Remove existing link if it exists
if (is_link($link)) {
    unlink($link);
    echo "Removed existing storage link\n";
}

// Create the symbolic link
if (symlink($target, $link)) {
    echo "✅ Storage link created successfully!\n";
    echo "Target: $target\n";
    echo "Link: $link\n";
} else {
    echo "❌ Failed to create storage link\n";
    
    // Alternative: Create directory structure manually
    if (!is_dir($link)) {
        mkdir($link, 0755, true);
        echo "✅ Created storage directory manually\n";
    }
}

// Create po_images directory
$poImagesDir = __DIR__ . '/storage/app/public/po_images';
if (!is_dir($poImagesDir)) {
    mkdir($poImagesDir, 0755, true);
    echo "✅ Created po_images directory\n";
}

echo "\n🎉 Storage setup complete!\n";
