<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, <PERSON> } from '@inertiajs/vue3';
import { Edit, ArrowLeft } from 'lucide-vue-next';

interface PoRecievedItem {
    id: number;
    product_name: string;
    quantity: number;
    price: number;
    total: number;
}

interface PoRecieved {
    id: number;
    po_number: string;
    po_date: string;
    po_image?: string;
    institution_name: string;
    address: string;
    email: string;
    phone: string;
    total_amount: number;
    created_at: string;
    user: {
        id: number;
        name: string;
    };
    items: PoRecievedItem[];
}

interface Props {
    poRecieved: PoRecieved;
}

defineProps<Props>();

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};
</script>

<template>
    <Head :title="`PO Recieved - ${poRecieved.institution_name}`" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <Button variant="outline" size="sm" as-child>
                        <Link :href="route('po-recieved.index')">
                            <ArrowLeft class="mr-2 h-4 w-4" />
                            Back
                        </Link>
                    </Button>
                    <div>
                        <h1 class="text-3xl font-bold tracking-tight">{{ poRecieved.institution_name }}</h1>
                        <p class="text-muted-foreground">
                            PO #{{ poRecieved.po_number }} • Created by {{ poRecieved.user.name }} on {{ formatDate(poRecieved.created_at) }}
                        </p>
                    </div>
                </div>
                <Button as-child>
                    <Link :href="route('po-recieved.edit', poRecieved.id)">
                        <Edit class="mr-2 h-4 w-4" />
                        Edit
                    </Link>
                </Button>
            </div>

            <!-- PO Details -->
            <Card>
                <CardHeader>
                    <CardTitle>PO Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="font-medium text-sm text-muted-foreground">PO Number</h4>
                            <p class="text-lg">{{ poRecieved.po_number }}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-sm text-muted-foreground">PO Date</h4>
                            <p class="text-lg">{{ formatDate(poRecieved.po_date) }}</p>
                        </div>
                        <div v-if="poRecieved.po_image">
                            <h4 class="font-medium text-sm text-muted-foreground">PO Image</h4>
                            <img :src="`/storage/${poRecieved.po_image}`" alt="PO Image" class="mt-2 max-w-xs rounded-lg border">
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Institution Details -->
            <Card>
                <CardHeader>
                    <CardTitle>Institution Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-sm text-muted-foreground">Institution Name</h4>
                            <p class="text-lg">{{ poRecieved.institution_name }}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-sm text-muted-foreground">Email</h4>
                            <p class="text-lg">{{ poRecieved.email }}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-sm text-muted-foreground">Phone</h4>
                            <p class="text-lg">{{ poRecieved.phone }}</p>
                        </div>
                        <div class="md:col-span-2">
                            <h4 class="font-medium text-sm text-muted-foreground">Address</h4>
                            <p class="text-lg">{{ poRecieved.address }}</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Items -->
            <Card>
                <CardHeader>
                    <CardTitle>Items</CardTitle>
                    <CardDescription>
                        {{ poRecieved.items.length }} items in this order
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="space-y-4">
                        <div v-for="(item, index) in poRecieved.items" :key="item.id" class="border rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Product</h4>
                                    <p class="text-lg">{{ item.product_name }}</p>
                                </div>
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Quantity</h4>
                                    <p class="text-lg">{{ item.quantity }}</p>
                                </div>
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Price</h4>
                                    <p class="text-lg">{{ formatCurrency(item.price) }}</p>
                                </div>
                                <div>
                                    <h4 class="font-medium text-sm text-muted-foreground">Total</h4>
                                    <p class="text-lg font-semibold">{{ formatCurrency(item.total) }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border-t pt-4">
                            <div class="flex justify-end">
                                <div class="text-right">
                                    <h4 class="font-medium text-sm text-muted-foreground">Grand Total</h4>
                                    <p class="text-2xl font-bold">{{ formatCurrency(poRecieved.total_amount) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
