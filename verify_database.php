<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "✅ Database connection successful!\n\n";

    // Check if tables exist
    $tables = ['users', 'po_recieved', 'po_recieved_items'];

    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists\n";

            // Show table structure
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($columns as $column) {
                echo "   - {$column['Field']} ({$column['Type']})\n";
            }
            echo "\n";
        } else {
            echo "❌ Table '$table' does not exist\n";
        }
    }

    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();

    if ($admin) {
        echo "✅ Admin user exists: {$admin['name']} ({$admin['email']})\n";
    } else {
        echo "❌ Admin user does not exist. Creating...\n";

        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW(), NOW())");
        $stmt->execute([
            'Admin User',
            '<EMAIL>',
            password_hash('password', PASSWORD_DEFAULT),
            'admin'
        ]);

        echo "✅ Admin user created successfully!\n";
    }

    // Create dummy users
    $dummyUsers = [
        ['name' => 'Test User', 'email' => '<EMAIL>'],
        ['name' => 'John Smith', 'email' => '<EMAIL>'],
        ['name' => 'Sarah Johnson', 'email' => '<EMAIL>'],
        ['name' => 'Mike Wilson', 'email' => '<EMAIL>']
    ];

    foreach ($dummyUsers as $userData) {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$userData['email']]);
        $existingUser = $stmt->fetch();

        if (!$existingUser) {
            echo "Creating user: {$userData['name']}...\n";
            $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW(), NOW())");
            $stmt->execute([
                $userData['name'],
                $userData['email'],
                password_hash('password', PASSWORD_DEFAULT),
                'user'
            ]);
            echo "✅ User created: {$userData['name']} ({$userData['email']})\n";
        } else {
            echo "✅ User exists: {$existingUser['name']} ({$existingUser['email']})\n";
        }
    }

    // Create initial stock movements for products
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM stock_movements");
    $stockCount = $stmt->fetch()['count'];

    if ($stockCount == 0) {
        echo "Creating initial stock movements...\n";

        $products = $pdo->query("SELECT * FROM products")->fetchAll();

        foreach ($products as $product) {
            $stmt = $pdo->prepare("INSERT INTO stock_movements (product_id, user_id, type, quantity, reference_type, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute([
                $product['id'],
                $admin['id'],
                'in',
                500,
                'initial',
                'Initial stock for ' . $product['name']
            ]);
        }

        echo "✅ Initial stock movements created!\n";
    }

    echo "\n🎉 Database setup complete!\n";
    echo "\nLogin credentials:\n";
    echo "Admin: <EMAIL> / password\n";
    echo "Users: <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";
    echo "       <EMAIL> / password\n";

    echo "\n📦 Available Products:\n";
    $products = $pdo->query("SELECT * FROM products")->fetchAll();
    foreach ($products as $product) {
        echo "- {$product['name']}: {$product['quantity']} units\n";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
