<?php

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=order_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful!\n\n";
    
    // Check if tables exist
    $tables = ['users', 'po_recieved', 'po_recieved_items'];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists\n";
            
            // Show table structure
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($columns as $column) {
                echo "   - {$column['Field']} ({$column['Type']})\n";
            }
            echo "\n";
        } else {
            echo "❌ Table '$table' does not exist\n";
        }
    }
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin user exists: {$admin['name']} ({$admin['email']})\n";
    } else {
        echo "❌ Admin user does not exist. Creating...\n";
        
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW(), NOW())");
        $stmt->execute([
            'Admin User',
            '<EMAIL>',
            password_hash('password', PASSWORD_DEFAULT),
            'admin'
        ]);
        
        echo "✅ Admin user created successfully!\n";
    }
    
    // Check if test user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $testUser = $stmt->fetch();
    
    if (!$testUser) {
        echo "Creating test user...\n";
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW(), NOW())");
        $stmt->execute([
            'Test User',
            '<EMAIL>',
            password_hash('password', PASSWORD_DEFAULT),
            'user'
        ]);
        echo "✅ Test user created successfully!\n";
    } else {
        echo "✅ Test user exists: {$testUser['name']} ({$testUser['email']})\n";
    }
    
    echo "\n🎉 Database setup complete!\n";
    echo "\nLogin credentials:\n";
    echo "Admin: <EMAIL> / password\n";
    echo "User: <EMAIL> / password\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
