<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/InputError.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';
import { ArrowLeft } from 'lucide-vue-next';

interface PoRecieved {
    id: number;
    po_number: string;
    institution_name: string;
    total_amount: number;
    user: {
        id: number;
        name: string;
    };
}

interface Props {
    suppliedPos: PoRecieved[];
}

const props = defineProps<Props>();

const form = useForm({
    po_recieved_id: '',
    cheque_number: '',
    cheque_image: null as File | null,
    amount: 0,
    payment_date: new Date().toISOString().split('T')[0],
    notes: ''
});

const handleFileChange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        form.cheque_image = target.files[0];
    }
};

const getSelectedPo = () => {
    if (!form.po_recieved_id) return null;
    return props.suppliedPos.find(po => po.id.toString() === form.po_recieved_id);
};

const submit = () => {
    form.post(route('payment.store'));
};

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};
</script>

<template>
    <Head title="Record Payment" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center space-x-4">
                <Button variant="outline" size="sm" as-child>
                    <Link :href="route('payment.index')">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back
                    </Link>
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Record Payment</h1>
                    <p class="text-muted-foreground">
                        Record payment details for a supplied PO
                    </p>
                </div>
            </div>

            <Card class="max-w-2xl">
                <CardHeader>
                    <CardTitle>Payment Details</CardTitle>
                    <CardDescription>
                        Enter payment information including cheque details
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <form @submit.prevent="submit" class="space-y-4">
                        <div class="space-y-2">
                            <Label for="po_recieved_id">Select PO</Label>
                            <select
                                id="po_recieved_id"
                                v-model="form.po_recieved_id"
                                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                required
                            >
                                <option value="">Select a supplied PO</option>
                                <option v-for="po in suppliedPos" :key="po.id" :value="po.id">
                                    {{ po.po_number }} - {{ po.institution_name }} ({{ formatCurrency(po.total_amount) }})
                                </option>
                            </select>
                            <InputError :message="form.errors.po_recieved_id" />
                        </div>

                        <div v-if="getSelectedPo()" class="p-4 bg-muted rounded-lg">
                            <h4 class="font-medium mb-2">Selected PO Details:</h4>
                            <p><strong>PO Number:</strong> {{ getSelectedPo()?.po_number }}</p>
                            <p><strong>Institution:</strong> {{ getSelectedPo()?.institution_name }}</p>
                            <p><strong>Total Amount:</strong> {{ formatCurrency(getSelectedPo()?.total_amount || 0) }}</p>
                            <p><strong>Created by:</strong> {{ getSelectedPo()?.user.name }}</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <Label for="cheque_number">Cheque Number</Label>
                                <Input
                                    id="cheque_number"
                                    v-model="form.cheque_number"
                                    type="text"
                                    placeholder="Enter cheque number"
                                    required
                                />
                                <InputError :message="form.errors.cheque_number" />
                            </div>

                            <div class="space-y-2">
                                <Label for="payment_date">Payment Date</Label>
                                <Input
                                    id="payment_date"
                                    v-model="form.payment_date"
                                    type="date"
                                    required
                                />
                                <InputError :message="form.errors.payment_date" />
                            </div>
                        </div>

                        <div class="space-y-2">
                            <Label for="amount">Amount</Label>
                            <Input
                                id="amount"
                                v-model.number="form.amount"
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="0.00"
                                required
                            />
                            <InputError :message="form.errors.amount" />
                        </div>

                        <div class="space-y-2">
                            <Label for="cheque_image">Cheque Image</Label>
                            <Input
                                id="cheque_image"
                                type="file"
                                accept="image/*"
                                @change="handleFileChange"
                            />
                            <p class="text-sm text-muted-foreground">
                                Upload an image of the cheque for verification
                            </p>
                            <InputError :message="form.errors.cheque_image" />
                        </div>

                        <div class="space-y-2">
                            <Label for="notes">Notes (Optional)</Label>
                            <textarea
                                id="notes"
                                v-model="form.notes"
                                class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                placeholder="Add any additional notes about this payment..."
                            ></textarea>
                            <InputError :message="form.errors.notes" />
                        </div>

                        <div class="flex items-center justify-end space-x-4 pt-4">
                            <Button type="button" variant="outline" as-child>
                                <Link :href="route('payment.index')">Cancel</Link>
                            </Button>
                            <Button type="submit" :disabled="form.processing">
                                {{ form.processing ? 'Recording...' : 'Record Payment' }}
                            </Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
