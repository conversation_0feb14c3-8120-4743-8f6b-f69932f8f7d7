<?php

use App\Http\Controllers\PoRecievedController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::resource('users', UserController::class);
});

// PO Recieved routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('po-recieved', PoRecievedController::class);
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
