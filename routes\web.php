<?php

use App\Http\Controllers\PoRecievedController;
use App\Http\Controllers\ReturnController;
use App\Http\Controllers\StockController;
use App\Http\Controllers\SupplyController;
use App\Http\Controllers\TransferController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::resource('users', UserController::class);
});

// PO Recieved routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('po-recieved', PoRecievedController::class);

    // Stock routes
    Route::get('stock', [StockController::class, 'index'])->name('stock.index');
    Route::get('stock/in', [StockController::class, 'stockIn'])->name('stock.in');
    Route::get('stock/out', [StockController::class, 'stockOut'])->name('stock.out');
    Route::post('stock/add', [StockController::class, 'addStock'])->name('stock.add');
    Route::post('stock/remove', [StockController::class, 'removeStock'])->name('stock.remove');

    // Supply routes
    Route::get('supply', [SupplyController::class, 'index'])->name('supply.index');
    Route::get('supply/{id}', [SupplyController::class, 'show'])->name('supply.show');
    Route::post('supply/{id}', [SupplyController::class, 'store'])->name('supply.store');

    // Transfer routes
    Route::resource('transfer', TransferController::class)->only(['index', 'create', 'store', 'show']);

    // Return routes
    Route::resource('return', ReturnController::class)->only(['index', 'create', 'store', 'show']);
    Route::post('return/{id}/approve', [ReturnController::class, 'approve'])->name('return.approve');
    Route::post('return/{id}/reject', [ReturnController::class, 'reject'])->name('return.reject');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
