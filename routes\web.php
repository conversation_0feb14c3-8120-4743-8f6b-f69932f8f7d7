<?php

use App\Http\Controllers\DocumentController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\PoRecievedController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ReturnController;
use App\Http\Controllers\StockController;
use App\Http\Controllers\SupplyController;
use App\Http\Controllers\TransferController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin routes
Route::middleware(['auth', 'verified'])->prefix('admin')->name('admin.')->group(function () {
    Route::resource('users', UserController::class);
});

// PO Recieved routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::resource('po-recieved', PoRecievedController::class);

    // Stock routes (using ProductController for stock management)
    Route::get('stock', [ProductController::class, 'index'])->name('stock.index');
    Route::get('stock/out', [StockController::class, 'stockOut'])->name('stock.out');
    Route::post('stock/add', [StockController::class, 'addStock'])->name('stock.add');
    Route::post('stock/remove', [StockController::class, 'removeStock'])->name('stock.remove');

    // Supply routes
    Route::get('supply', [SupplyController::class, 'index'])->name('supply.index');
    Route::get('supply/{id}', [SupplyController::class, 'show'])->name('supply.show');
    Route::post('supply/{id}', [SupplyController::class, 'store'])->name('supply.store');

    // Transfer routes
    Route::resource('transfer', TransferController::class)->only(['index', 'create', 'store', 'show']);

    // Return routes
    Route::resource('return', ReturnController::class)->only(['index', 'create', 'store', 'show']);
    Route::post('return/{id}/approve', [ReturnController::class, 'approve'])->name('return.approve');
    Route::post('return/{id}/reject', [ReturnController::class, 'reject'])->name('return.reject');

    // Document routes
    Route::get('documents', [DocumentController::class, 'index'])->name('documents.index');
    Route::post('documents/generate/{supply}', [DocumentController::class, 'generateDocuments'])->name('documents.generate');
    Route::get('documents/dc/{id}', [DocumentController::class, 'viewDc'])->name('documents.dc.view');
    Route::get('documents/dc/{id}/print', [DocumentController::class, 'printDc'])->name('documents.dc.print');
    Route::post('documents/dc/{id}/upload-proof', [DocumentController::class, 'uploadDcProof'])->name('documents.dc.upload-proof');
    Route::get('documents/invoice/{id}', [DocumentController::class, 'viewInvoice'])->name('documents.invoice.view');
    Route::get('documents/invoice/{id}/print', [DocumentController::class, 'printInvoice'])->name('documents.invoice.print');
    Route::post('documents/invoice/{id}/upload-proof', [DocumentController::class, 'uploadInvoiceProof'])->name('documents.invoice.upload-proof');

    // Product routes
    Route::resource('product', ProductController::class);
    Route::post('product/{id}/add-stock', [ProductController::class, 'addStock'])->name('product.add-stock');
    Route::post('product/{id}/remove-stock', [ProductController::class, 'removeStock'])->name('product.remove-stock');

    // Payment routes
    Route::resource('payment', PaymentController::class);
    Route::post('payment/{id}/approve', [PaymentController::class, 'approve'])->name('payment.approve');
    Route::post('payment/{id}/reject', [PaymentController::class, 'reject'])->name('payment.reject');
});

// API routes for AJAX calls
Route::middleware(['auth', 'verified'])->prefix('api')->group(function () {
    Route::get('po/{id}/latest-supply', [SupplyController::class, 'getLatestSupply'])->name('api.po.latest-supply');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
