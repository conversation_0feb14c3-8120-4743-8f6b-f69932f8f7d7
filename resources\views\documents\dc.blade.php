<!DOCTYPE html>
<html>
<head>
    <title>Delivery Challan - {{ $dc->dc_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 14px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .document-title {
            font-size: 20px;
            font-weight: bold;
            margin-top: 20px;
        }
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .info-box {
            width: 48%;
        }
        .info-box h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .total-section {
            margin-top: 20px;
            text-align: right;
        }
        .total-amount {
            font-size: 18px;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            border-top: 1px solid #ccc;
            padding-top: 20px;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
        }
        .signature-box {
            width: 30%;
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 10px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="no-print" style="margin-bottom: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Print DC</button>
        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Close</button>
    </div>

    <div class="header">
        <div class="company-name">{{ config('app.name', 'Your Company Name') }}</div>
        <div>123 Business Street, City, State 12345</div>
        <div>Phone: (************* | Email: <EMAIL></div>
        <div class="document-title">DELIVERY CHALLAN</div>
    </div>

    <div class="info-section" style="display: flex;">
        <div class="info-box">
            <h3>DC Details</h3>
            <p><strong>DC Number:</strong> {{ $dc->dc_number }}</p>
            <p><strong>DC Date:</strong> {{ $dc->dc_date->format('d/m/Y') }}</p>
            <p><strong>PO Number:</strong> {{ $dc->supply->poRecieved->po_number }}</p>
        </div>
        <div class="info-box">
            <h3>Delivery To</h3>
            <p><strong>{{ $dc->supply->poRecieved->institution_name }}</strong></p>
            <p>{{ $dc->supply->poRecieved->address }}</p>
            <p>Email: {{ $dc->supply->poRecieved->email }}</p>
            <p>Phone: {{ $dc->supply->poRecieved->phone }}</p>
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>S.No.</th>
                <th>Product Name</th>
                <th>Quantity Supplied</th>
                <th>Rate</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            @foreach($dc->supply->items as $index => $item)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $item->poRecievedItem->product_name }}</td>
                <td class="text-right">{{ $item->quantity_supplied }}</td>
                <td class="text-right">${{ number_format($item->poRecievedItem->price, 2) }}</td>
                <td class="text-right">${{ number_format($item->quantity_supplied * $item->poRecievedItem->price, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="total-section">
        <div class="total-amount">
            <strong>Total Amount: ${{ number_format($dc->total_amount, 2) }}</strong>
        </div>
    </div>

    @if($dc->notes)
    <div style="margin-top: 20px;">
        <h3>Notes:</h3>
        <p>{{ $dc->notes }}</p>
    </div>
    @endif

    <div class="signature-section" style="display: flex;">
        <div class="signature-box">
            <div>Prepared By</div>
            <div style="margin-top: 20px;">{{ $dc->supply->user->name }}</div>
        </div>
        <div class="signature-box">
            <div>Authorized Signatory</div>
        </div>
        <div class="signature-box">
            <div>Received By</div>
        </div>
    </div>

    <div class="footer">
        <p style="text-align: center; font-size: 12px; color: #666;">
            This is a computer generated document. No signature required.
        </p>
    </div>
</body>
</html>
