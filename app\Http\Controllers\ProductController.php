<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\StockMovement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index(): Response
    {
        $products = Product::orderBy('name')->paginate(10);

        return Inertia::render('Product/Index', [
            'products' => $products,
        ]);
    }

    /**
     * Show the form for creating a new product
     */
    public function create(): Response
    {
        return Inertia::render('Product/Create');
    }

    /**
     * Store a newly created product
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:products,name',
            'initial_quantity' => 'nullable|integer|min:0',
        ]);

        $product = Product::create([
            'name' => $request->name,
            'quantity' => 0, // Will be updated by stock movement
        ]);

        // Create initial stock movement if quantity provided
        if ($request->initial_quantity > 0) {
            StockMovement::create([
                'product_id' => $product->id,
                'user_id' => auth()->id(),
                'type' => 'in',
                'quantity' => $request->initial_quantity,
                'reference_type' => 'initial',
                'notes' => 'Initial stock for ' . $product->name,
            ]);
        }

        return redirect()->route('product.index')
            ->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified product
     */
    public function show($id): Response
    {
        $product = Product::with(['stockMovements.user'])->findOrFail($id);

        return Inertia::render('Product/Show', [
            'product' => $product,
        ]);
    }

    /**
     * Show the form for editing the specified product
     */
    public function edit($id): Response
    {
        $product = Product::findOrFail($id);

        return Inertia::render('Product/Edit', [
            'product' => $product,
        ]);
    }

    /**
     * Update the specified product
     */
    public function update(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255|unique:products,name,' . $product->id,
        ]);

        $product->update([
            'name' => $request->name,
        ]);

        return redirect()->route('product.index')
            ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified product
     */
    public function destroy($id)
    {
        $product = Product::findOrFail($id);

        // Check if product has stock movements
        if ($product->stockMovements()->count() > 0) {
            return redirect()->back()
                ->with('error', 'Cannot delete product with existing stock movements.');
        }

        $product->delete();

        return redirect()->route('product.index')
            ->with('success', 'Product deleted successfully.');
    }

    /**
     * Add stock to product
     */
    public function addStock(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        StockMovement::create([
            'product_id' => $product->id,
            'user_id' => auth()->id(),
            'type' => 'in',
            'quantity' => $request->quantity,
            'reference_type' => 'manual',
            'notes' => $request->notes ?? 'Manual stock addition',
        ]);

        return redirect()->back()
            ->with('success', 'Stock added successfully.');
    }

    /**
     * Remove stock from product
     */
    public function removeStock(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($product->quantity < $request->quantity) {
            return redirect()->back()
                ->with('error', 'Insufficient stock available.');
        }

        StockMovement::create([
            'product_id' => $product->id,
            'user_id' => auth()->id(),
            'type' => 'out',
            'quantity' => $request->quantity,
            'reference_type' => 'manual',
            'notes' => $request->notes ?? 'Manual stock removal',
        ]);

        return redirect()->back()
            ->with('success', 'Stock removed successfully.');
    }
}
