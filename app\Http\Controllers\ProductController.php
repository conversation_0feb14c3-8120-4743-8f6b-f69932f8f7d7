<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\StockMovement;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ProductController extends Controller
{
    /**
     * Display a listing of products
     */
    public function index(): Response
    {
        $products = Product::with(['stockMovements.user:id,name'])
            ->orderBy('name')
            ->paginate(10);

        return Inertia::render('Stock/Index', [
            'products' => $products,
        ]);
    }

    /**
     * Show the form for creating a new product
     */
    public function create(): Response
    {
        return Inertia::render('Product/Create');
    }

    /**
     * Store a newly created product
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:products,name',
            'initial_quantity' => 'nullable|integer|min:0',
        ]);

        $product = Product::create([
            'name' => $request->name,
            'quantity' => 0, // Will be updated by stock movement
        ]);

        // Create initial stock movement if quantity provided
        if ($request->initial_quantity > 0) {
            StockMovement::create([
                'product_id' => $product->id,
                'user_id' => auth()->id(),
                'type' => 'in',
                'quantity' => $request->initial_quantity,
                'reference_type' => 'initial',
                'notes' => 'Initial stock for ' . $product->name,
            ]);
        }

        return redirect()->route('product.index')
            ->with('success', 'Product created successfully.');
    }

    /**
     * Update the specified product
     */
    public function update(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255|unique:products,name,' . $id,
            'quantity' => 'required|integer|min:0',
        ]);

        $oldQuantity = $product->quantity;
        $newQuantity = $request->quantity;

        $product->update([
            'name' => $request->name,
            'quantity' => $newQuantity,
        ]);

        // Create stock movement if quantity changed
        if ($oldQuantity !== $newQuantity) {
            $difference = $newQuantity - $oldQuantity;
            StockMovement::create([
                'product_id' => $product->id,
                'user_id' => auth()->id(),
                'type' => $difference > 0 ? 'in' : 'out',
                'quantity' => abs($difference),
                'reference_type' => 'adjustment',
                'notes' => "Manual adjustment: {$oldQuantity} → {$newQuantity}",
            ]);
        }

        return redirect()->route('product.index')
            ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified product
     */
    public function destroy($id)
    {
        $product = Product::findOrFail($id);

        // Check if product has been used in supplies
        $hasSupplies = \App\Models\SupplyItem::whereHas('poRecievedItem', function($query) use ($product) {
            $query->where('product_name', $product->name);
        })->exists();

        if ($hasSupplies) {
            return redirect()->route('product.index')
                ->with('error', 'Cannot delete product that has been used in supplies.');
        }

        $product->delete();

        return redirect()->route('product.index')
            ->with('success', 'Product deleted successfully.');
    }

    /**
     * Add stock to product
     */
    public function addStock(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
        ]);

        $product->increment('quantity', $request->quantity);

        StockMovement::create([
            'product_id' => $product->id,
            'user_id' => auth()->id(),
            'type' => 'in',
            'quantity' => $request->quantity,
            'reference_type' => 'manual_add',
            'notes' => $request->notes ?: "Added {$request->quantity} units to {$product->name}",
        ]);

        return redirect()->route('product.index')
            ->with('success', "Added {$request->quantity} units to {$product->name}.");
    }

    /**
     * Remove stock from product
     */
    public function removeStock(Request $request, $id)
    {
        $product = Product::findOrFail($id);

        $request->validate([
            'quantity' => 'required|integer|min:1|max:' . $product->quantity,
            'notes' => 'nullable|string|max:500',
        ]);

        $product->decrement('quantity', $request->quantity);

        StockMovement::create([
            'product_id' => $product->id,
            'user_id' => auth()->id(),
            'type' => 'out',
            'quantity' => $request->quantity,
            'reference_type' => 'manual_remove',
            'notes' => $request->notes ?: "Removed {$request->quantity} units from {$product->name}",
        ]);

        return redirect()->route('product.index')
            ->with('success', "Removed {$request->quantity} units from {$product->name}.");
    }

    /**
     * Display the specified product
     */
    public function show($id): Response
    {
        $product = Product::with(['stockMovements.user'])->findOrFail($id);

        return Inertia::render('Product/Show', [
            'product' => $product,
        ]);
    }

    /**
     * Show the form for editing the specified product
     */
    public function edit($id): Response
    {
        $product = Product::findOrFail($id);

        return Inertia::render('Product/Edit', [
            'product' => $product,
        ]);
    }


}
