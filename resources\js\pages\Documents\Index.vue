<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, router } from '@inertiajs/vue3';
import { FileText, Receipt, Upload, Eye, Download } from 'lucide-vue-next';
import { ref } from 'vue';

interface DeliveryChallan {
    id: number;
    dc_number: string;
    dc_date: string;
    total_amount: number;
    dc_proof_image: string | null;
    created_at: string;
    supply: {
        poRecieved: {
            po_number: string;
            institution_name: string;
        };
    };
}

interface Invoice {
    id: number;
    invoice_number: string;
    invoice_date: string;
    total_amount: number;
    invoice_proof_image: string | null;
    created_at: string;
    supply: {
        poRecieved: {
            po_number: string;
            institution_name: string;
        };
    };
}

interface Props {
    dcs: {
        data: DeliveryChallan[];
        links: any;
        meta: any;
    };
    invoices: {
        data: Invoice[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const uploadingDc = ref<number | null>(null);
const uploadingInvoice = ref<number | null>(null);

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const uploadDcProof = (dcId: number, event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        uploadingDc.value = dcId;
        
        const formData = new FormData();
        formData.append('dc_proof_image', target.files[0]);
        formData.append('_method', 'PUT');
        
        fetch(`/documents/dc/${dcId}/upload-proof`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                router.reload();
            } else {
                alert('Error uploading proof: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error uploading proof');
        })
        .finally(() => {
            uploadingDc.value = null;
        });
    }
};

const uploadInvoiceProof = (invoiceId: number, event: Event) => {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files[0]) {
        uploadingInvoice.value = invoiceId;
        
        const formData = new FormData();
        formData.append('invoice_proof_image', target.files[0]);
        formData.append('_method', 'PUT');
        
        fetch(`/documents/invoice/${invoiceId}/upload-proof`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                router.reload();
            } else {
                alert('Error uploading proof: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error uploading proof');
        })
        .finally(() => {
            uploadingInvoice.value = null;
        });
    }
};

const viewDocument = (type: 'dc' | 'invoice', id: number) => {
    window.open(`/documents/${type}/${id}/print`, '_blank');
};
</script>

<template>
    <Head title="Documents" />

    <AppLayout>
        <div class="space-y-6">
            <div>
                <h1 class="text-3xl font-bold tracking-tight">Documents</h1>
                <p class="text-muted-foreground">
                    Manage delivery challans and invoices
                </p>
            </div>

            <!-- Delivery Challans -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <FileText class="h-5 w-5" />
                        Delivery Challans
                    </CardTitle>
                    <CardDescription>
                        View and manage delivery challans with proof uploads
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="space-y-4">
                        <div v-for="dc in dcs.data" :key="dc.id" class="border rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h3 class="font-semibold">{{ dc.dc_number }}</h3>
                                    <p class="text-sm text-muted-foreground">
                                        PO: {{ dc.supply.poRecieved.po_number }} • {{ dc.supply.poRecieved.institution_name }}
                                    </p>
                                    <p class="text-sm text-muted-foreground">
                                        Date: {{ formatDate(dc.dc_date) }} • Amount: {{ formatCurrency(dc.total_amount) }}
                                    </p>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <!-- Proof Upload -->
                                    <div class="flex flex-col items-center space-y-2">
                                        <div v-if="dc.dc_proof_image" class="text-center">
                                            <img 
                                                :src="`/storage/${dc.dc_proof_image}`" 
                                                alt="DC Proof" 
                                                class="w-16 h-16 object-cover rounded border cursor-pointer"
                                                @click="window.open(`/storage/${dc.dc_proof_image}`, '_blank')"
                                            />
                                            <p class="text-xs text-green-600 mt-1">Proof Uploaded</p>
                                        </div>
                                        <div v-else class="text-center">
                                            <div class="w-16 h-16 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
                                                <Upload class="h-6 w-6 text-gray-400" />
                                            </div>
                                            <p class="text-xs text-gray-500 mt-1">No Proof</p>
                                        </div>
                                        
                                        <label class="cursor-pointer">
                                            <input 
                                                type="file" 
                                                accept="image/*" 
                                                class="hidden"
                                                @change="uploadDcProof(dc.id, $event)"
                                                :disabled="uploadingDc === dc.id"
                                            />
                                            <Button size="sm" variant="outline" :disabled="uploadingDc === dc.id">
                                                <Upload class="h-3 w-3 mr-1" />
                                                {{ uploadingDc === dc.id ? 'Uploading...' : 'Upload Proof' }}
                                            </Button>
                                        </label>
                                    </div>
                                    
                                    <!-- View/Download -->
                                    <Button size="sm" @click="viewDocument('dc', dc.id)">
                                        <Eye class="h-3 w-3 mr-1" />
                                        View PDF
                                    </Button>
                                </div>
                            </div>
                        </div>
                        
                        <div v-if="dcs.data.length === 0" class="text-center py-8">
                            <FileText class="mx-auto h-12 w-12 text-muted-foreground" />
                            <h3 class="mt-2 text-sm font-semibold text-gray-900">No delivery challans</h3>
                            <p class="mt-1 text-sm text-muted-foreground">Delivery challans will appear here after processing supplies.</p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <!-- Invoices -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Receipt class="h-5 w-5" />
                        Invoices
                    </CardTitle>
                    <CardDescription>
                        View and manage invoices with proof uploads
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="space-y-4">
                        <div v-for="invoice in invoices.data" :key="invoice.id" class="border rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <h3 class="font-semibold">{{ invoice.invoice_number }}</h3>
                                    <p class="text-sm text-muted-foreground">
                                        PO: {{ invoice.supply.poRecieved.po_number }} • {{ invoice.supply.poRecieved.institution_name }}
                                    </p>
                                    <p class="text-sm text-muted-foreground">
                                        Date: {{ formatDate(invoice.invoice_date) }} • Amount: {{ formatCurrency(invoice.total_amount) }}
                                    </p>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <!-- Proof Upload -->
                                    <div class="flex flex-col items-center space-y-2">
                                        <div v-if="invoice.invoice_proof_image" class="text-center">
                                            <img 
                                                :src="`/storage/${invoice.invoice_proof_image}`" 
                                                alt="Invoice Proof" 
                                                class="w-16 h-16 object-cover rounded border cursor-pointer"
                                                @click="window.open(`/storage/${invoice.invoice_proof_image}`, '_blank')"
                                            />
                                            <p class="text-xs text-green-600 mt-1">Proof Uploaded</p>
                                        </div>
                                        <div v-else class="text-center">
                                            <div class="w-16 h-16 border-2 border-dashed border-gray-300 rounded flex items-center justify-center">
                                                <Upload class="h-6 w-6 text-gray-400" />
                                            </div>
                                            <p class="text-xs text-gray-500 mt-1">No Proof</p>
                                        </div>
                                        
                                        <label class="cursor-pointer">
                                            <input 
                                                type="file" 
                                                accept="image/*" 
                                                class="hidden"
                                                @change="uploadInvoiceProof(invoice.id, $event)"
                                                :disabled="uploadingInvoice === invoice.id"
                                            />
                                            <Button size="sm" variant="outline" :disabled="uploadingInvoice === invoice.id">
                                                <Upload class="h-3 w-3 mr-1" />
                                                {{ uploadingInvoice === invoice.id ? 'Uploading...' : 'Upload Proof' }}
                                            </Button>
                                        </label>
                                    </div>
                                    
                                    <!-- View/Download -->
                                    <Button size="sm" @click="viewDocument('invoice', invoice.id)">
                                        <Eye class="h-3 w-3 mr-1" />
                                        View PDF
                                    </Button>
                                </div>
                            </div>
                        </div>
                        
                        <div v-if="invoices.data.length === 0" class="text-center py-8">
                            <Receipt class="mx-auto h-12 w-12 text-muted-foreground" />
                            <h3 class="mt-2 text-sm font-semibold text-gray-900">No invoices</h3>
                            <p class="mt-1 text-sm text-muted-foreground">Invoices will appear here after processing supplies.</p>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>
