<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { TrendingUp } from 'lucide-vue-next';

interface StockMovement {
    id: number;
    quantity: number;
    reference_type: string;
    notes: string;
    created_at: string;
    product: {
        id: number;
        name: string;
    };
    user: {
        id: number;
        name: string;
    };
}

interface Props {
    stockMovements: {
        data: StockMovement[];
        links: any;
        meta: any;
    };
}

defineProps<Props>();

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};
</script>

<template>
    <Head title="Stock In" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Stock In</h1>
                    <p class="text-muted-foreground">
                        View all incoming stock movements
                    </p>
                </div>
            </div>

            <div class="grid gap-4">
                <Card v-for="movement in stockMovements.data" :key="movement.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    <TrendingUp class="h-5 w-5 text-green-600" />
                                    {{ movement.product.name }}
                                </CardTitle>
                                <CardDescription>
                                    Added by {{ movement.user.name }} on {{ formatDate(movement.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="text-right">
                                <p class="text-2xl font-bold text-green-600">+{{ movement.quantity }}</p>
                                <p class="text-sm text-muted-foreground">{{ movement.reference_type }}</p>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent v-if="movement.notes">
                        <p class="text-sm text-muted-foreground">{{ movement.notes }}</p>
                    </CardContent>
                </Card>

                <div v-if="stockMovements.data.length === 0" class="text-center py-12">
                    <TrendingUp class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No stock movements</h3>
                    <p class="mt-1 text-sm text-muted-foreground">No incoming stock movements found.</p>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
