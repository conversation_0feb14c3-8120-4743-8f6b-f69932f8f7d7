<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';
import { Plus, Eye, Edit, Trash2, Package } from 'lucide-vue-next';

interface PoRecievedItem {
    id: number;
    product_name: string;
    quantity: number;
    unit: string;
    price: number;
    total: number;
}

interface PoRecieved {
    id: number;
    po_number: string;
    po_date: string;
    po_image?: string;
    institution_name: string;
    address: string;
    email: string;
    phone: string;
    total_amount: number;
    status: 'pending' | 'partially_supplied' | 'supplied';
    created_at: string;
    user: {
        id: number;
        name: string;
    };
    items: PoRecievedItem[];
}

interface Props {
    poRecieved: {
        data: PoRecieved[];
        links: any;
        meta: any;
    };
}

const props = defineProps<Props>();

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
};

const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
};

const deletePoRecieved = (id: number) => {
    if (confirm('Are you sure you want to delete this PO Recieved?')) {
        router.delete(route('po-recieved.destroy', id), {
            onSuccess: () => {
                // Success message will be shown via Laravel's session flash
            },
            onError: () => {
                alert('Error deleting PO Recieved. Please try again.');
            }
        });
    }
};

const getStatusBadgeVariant = (status: string) => {
    switch (status) {
        case 'pending':
            return 'secondary';
        case 'partially_supplied':
            return 'default';
        case 'supplied':
            return 'default';
        default:
            return 'secondary';
    }
};

const getStatusColor = (status: string) => {
    switch (status) {
        case 'pending':
            return 'text-yellow-600 bg-yellow-100';
        case 'partially_supplied':
            return 'text-blue-600 bg-blue-100';
        case 'supplied':
            return 'text-green-600 bg-green-100';
        default:
            return 'text-gray-600 bg-gray-100';
    }
};
</script>

<template>
    <Head title="PO Recieved" />

    <AppLayout>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">PO Recieved</h1>
                    <p class="text-muted-foreground">
                        Manage purchase orders recieved from institutions
                    </p>
                </div>
                <Button as-child>
                    <Link :href="route('po-recieved.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        Add PO Recieved
                    </Link>
                </Button>
            </div>

            <div class="grid gap-4">
                <Card v-for="po in poRecieved.data" :key="po.id">
                    <CardHeader>
                        <div class="flex items-center justify-between">
                            <div>
                                <CardTitle class="flex items-center gap-2">
                                    {{ po.institution_name }}
                                    <span :class="`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(po.status)}`">
                                        {{ po.status.replace('_', ' ').toUpperCase() }}
                                    </span>
                                </CardTitle>
                                <CardDescription>
                                    PO #{{ po.po_number }} • Created by {{ po.user.name }} on {{ formatDate(po.created_at) }}
                                </CardDescription>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button variant="outline" size="sm" as-child>
                                    <Link :href="route('po-recieved.show', po.id)">
                                        <Eye class="h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button variant="outline" size="sm" as-child>
                                    <Link :href="route('po-recieved.edit', po.id)">
                                        <Edit class="h-4 w-4" />
                                    </Link>
                                </Button>
                                <Button variant="destructive" size="sm" @click="deletePoRecieved(po.id)">
                                    <Trash2 class="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div class="grid grid-cols-2 md:grid-cols-6 gap-4 text-sm">
                            <div>
                                <p class="font-medium">PO Number</p>
                                <p class="text-muted-foreground">PO #{{ po.po_number }}</p>
                            </div><div>
                                <p class="font-medium">PO Date</p>
                                <p class="text-muted-foreground">{{ formatDate(po.po_date) }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Email</p>
                                <p class="text-muted-foreground">{{ po.email }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Phone</p>
                                <p class="text-muted-foreground">{{ po.phone }}</p>
                            </div>
                            <div>
                                <p class="font-medium">Items</p>
                                <p class="text-muted-foreground">{{ po.items.length }} items</p>
                            </div>
                            <div>
                                <p class="font-medium">Total Amount</p>
                                <p class="text-muted-foreground font-semibold">{{ formatCurrency(po.total_amount) }}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <div v-if="poRecieved.data.length === 0" class="text-center py-12">
                    <Package class="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No PO recieved</h3>
                    <p class="mt-1 text-sm text-muted-foreground">Get started by creating a new PO recieved.</p>
                    <div class="mt-6">
                        <Button as-child>
                            <Link :href="route('po-recieved.create')">
                                <Plus class="mr-2 h-4 w-4" />
                                Add PO Recieved
                            </Link>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>
